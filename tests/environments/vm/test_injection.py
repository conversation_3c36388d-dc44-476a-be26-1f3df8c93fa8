#!/usr/bin/env python3

"""
Test script for VM injection.
"""

import os
import sys
import logging
import async<PERSON>
import argparse
from typing import Dict, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_injection(vm_id: str, source_path: str, target_path: str, use_ssh: bool = True) -> None:
    """
    Test VM injection.
    
    Args:
        vm_id: The ID of the VM.
        source_path: The path to the source file.
        target_path: The path to the target file.
        use_ssh: Whether to use SSH mode.
    """
    from api.services.vagrant_client import VagrantClient
    
    # Create a VagrantClient instance
    vagrant_client = VagrantClient()
    vagrant_client.use_ssh = use_ssh
    
    # Connect to the service
    connected = await vagrant_client.connect()
    
    if not connected:
        logger.error(f"Failed to connect to Vagrant service using {'SSH' if use_ssh else 'gRPC'} mode")
        return
    
    logger.info(f"Successfully connected to Vagrant service using {'SSH' if use_ssh else 'gRPC'} mode")
    
    # Get the VM status
    status = await vagrant_client.status(vm_id)
    logger.info(f"VM status: {status}")
    
    # Inject the file
    logger.info(f"Injecting file {source_path} into VM {vm_id} at {target_path}")
    
    # Create the command to copy the file to the VM
    copy_command = f"cp {source_path} {target_path} && chmod 0755 {target_path}"
    
    # Execute the command
    result = await vagrant_client.execute_command(vm_id, copy_command, sudo=True)
    logger.info(f"File injection result: {result}")
    
    # Verify the file injection
    verify_command = f"ls -la {target_path}"
    verify_result = await vagrant_client.execute_command(vm_id, verify_command)
    logger.info(f"File verification result: {verify_result}")
    
    # Read the file content
    read_command = f"cat {target_path}"
    read_result = await vagrant_client.execute_command(vm_id, read_command)
    logger.info(f"File content: {read_result.get('stdout', '')}")
    
    # Close the client
    await vagrant_client.close()

def parse_args() -> argparse.Namespace:
    """
    Parse command line arguments.
    
    Returns:
        The parsed arguments.
    """
    parser = argparse.ArgumentParser(description='Test VM injection.')
    parser.add_argument('--vm-id', type=str, default='vm-injection-test',
                        help='The ID of the VM.')
    parser.add_argument('--source-path', type=str, default='test_file.txt',
                        help='The path to the source file.')
    parser.add_argument('--target-path', type=str, default='/tmp/injection_test/test_file.txt',
                        help='The path to the target file.')
    parser.add_argument('--use-grpc', action='store_true',
                        help='Use gRPC mode instead of SSH mode.')
    
    return parser.parse_args()

async def main() -> None:
    """
    Main function.
    """
    # Parse command line arguments
    args = parse_args()
    
    # Test VM injection
    await test_injection(
        vm_id=args.vm_id,
        source_path=args.source_path,
        target_path=args.target_path,
        use_ssh=not args.use_grpc
    )

if __name__ == '__main__':
    # Run the main function
    asyncio.run(main())
