# VM Injection Test Environment

This directory contains a test environment for the VM injection functionality.

## Setup

The test environment consists of a Vagrant VM that can be used to test the VM injection service.

### Prerequisites

- [Vagrant](https://www.vagrantup.com/)
- [VirtualBox](https://www.virtualbox.org/)

### Starting the VM

To start the VM, run:

```bash
./start_vm.sh
```

This will start the VM and print information about how to connect to it.

### Testing VM Injection

To test VM injection, run:

```bash
python3 test_injection.py
```

This will inject the `test_file.txt` file into the VM at `/tmp/injection_test/test_file.txt`.

You can customize the test by passing command line arguments:

```bash
python3 test_injection.py --vm-id <vm-id> --source-path <source-path> --target-path <target-path> [--use-grpc]
```

- `--vm-id`: The ID of the VM (default: `vm-injection-test`).
- `--source-path`: The path to the source file (default: `test_file.txt`).
- `--target-path`: The path to the target file (default: `/tmp/injection_test/test_file.txt`).
- `--use-grpc`: Use gRPC mode instead of SSH mode.

### Stopping the VM

To stop the VM, run:

```bash
./stop_vm.sh
```

## VM Configuration

The VM is configured with the following settings:

- Ubuntu 20.04 LTS (Focal Fossa)
- 1GB RAM
- 1 CPU
- Private network with DHCP
- Port forwarding: guest port 22 -> host port 2222
- Hostname: `vm-injection-test`

### Users

The VM has the following users:

- `vagrant`: The default Vagrant user.
- `testuser`: A test user for file injection.
  - Password: `password`
  - Sudo access: Yes

### Test Directory

The VM has a test directory for file injection:

- `/tmp/injection_test`: A directory with permissions 777 for file injection.

## Troubleshooting

If you encounter any issues, try the following:

- Check the VM status: `vagrant status`
- SSH into the VM: `vagrant ssh`
- Restart the VM: `vagrant reload`
- Destroy and recreate the VM: `vagrant destroy -f && vagrant up`
