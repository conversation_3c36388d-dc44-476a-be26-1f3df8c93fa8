#!/bin/bash

# Script to start the VM and test VM injection

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Starting VM for injection testing...${NC}"

# Change to the VM directory
cd "$(dirname "$0")"

# Check if the VM is already running
if vagrant status | grep -q "running"; then
    echo -e "${GREEN}VM is already running.${NC}"
else
    # Start the VM
    echo -e "${YELLOW}Starting VM...${NC}"
    vagrant up
    
    # Check if the VM started successfully
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to start VM!${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}VM started successfully.${NC}"
fi

# Get the VM's IP address
VM_IP=$(vagrant ssh -c "hostname -I | awk '{print \$1}'" -- -q)

echo -e "${GREEN}VM is running at IP: ${VM_IP}${NC}"
echo -e "${YELLOW}You can SSH into the VM using:${NC}"
echo -e "  vagrant ssh"
echo -e "${YELLOW}Or:${NC}"
echo -e "  ssh -p 2222 vagrant@localhost"
echo -e "${YELLOW}Or:${NC}"
echo -e "  ssh vagrant@${VM_IP}"
echo -e "${YELLOW}Or for the test user:${NC}"
echo -e "  ssh -p 2222 testuser@localhost"
echo -e "${YELLOW}Password for testuser: password${NC}"

# Print instructions for testing VM injection
echo -e "\n${YELLOW}To test VM injection, run:${NC}"
echo -e "  python3 test_injection.py"

# Exit successfully
exit 0
