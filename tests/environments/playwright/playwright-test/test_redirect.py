from playwright.sync_api import sync_playwright
import sys
import os
import time

def test_api_root_redirect():
    # Get the API host from environment variable or use default
    api_host = os.environ.get('API_HOST', 'api')
    api_port = os.environ.get('API_PORT', '8000')
    api_url = f"http://{api_host}:{api_port}/api/v1/"

    print(f"Testing redirect from {api_url}")

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=True)
        page = browser.new_page()

        try:
            # Navigate to the API root
            print(f"Navigating to {api_url}")
            response = page.goto(api_url, wait_until='networkidle')

            # Wait a moment to ensure any redirects complete
            time.sleep(2)

            # Get the current URL after redirect
            current_url = page.url
            print(f"Redirected to: {current_url}")

            # Take a screenshot
            page.screenshot(path='/app/redirect_result.png')
            print(f"Screenshot saved to /app/redirect_result.png")

            # Check if the redirect is correct
            expected_url = f"http://{api_host}:{api_port}/api/v1/docs/all/docs"
            if current_url == expected_url:
                print("SUCCESS: Redirect is correct")
                return 0
            else:
                print(f"ERROR: Redirect is incorrect. Expected {expected_url} but got {current_url}")
                return 1

        except Exception as e:
            print(f"ERROR: Test failed with exception: {str(e)}")
            return 2
        finally:
            browser.close()

if __name__ == "__main__":
    exit_code = test_api_root_redirect()
    sys.exit(exit_code)
