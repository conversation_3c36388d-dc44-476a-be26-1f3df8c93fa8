{"name": "vm-management-frontend", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^4.1.3", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@types/node": "^20.11.19", "chart.js": "^4.4.1", "notistack": "^3.0.1", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.55.0", "react-router-dom": "^6.22.1", "react-scripts": "^5.0.1", "typescript": "^5.3.3", "yup": "^1.6.1"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/chart.js": "^2.9.41", "@types/jest": "^29.5.14", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@types/react-router-dom": "^5.3.3"}, "overrides": {"nth-check": ">=2.0.1", "postcss": ">=8.4.31", "svgo": ">=2.0.0"}}