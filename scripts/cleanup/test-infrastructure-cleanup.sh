#!/bin/bash

# TurdParty Test Infrastructure Cleanup Script
# Implements the cleanup strategy outlined in test-infrastructure-cleanup-prd.md

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CLEANUP_DATE=$(date +%Y-%m-%d)
ARCHIVE_DIR="_archive_/test-cleanup-$CLEANUP_DATE"
LOG_FILE="logs/cleanup/test-infrastructure-cleanup-$CLEANUP_DATE.log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo -e "${GREEN}[INFO]${NC} $message" ;;
        WARN)  echo -e "${YELLOW}[WARN]${NC} $message" ;;
        ERROR) echo -e "${RED}[ERROR]${NC} $message" ;;
        DEBUG) echo -e "${BLUE}[DEBUG]${NC} $message" ;;
    esac
    
    # Also log to file
    mkdir -p "$(dirname "$LOG_FILE")"
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

# Function to check if directory exists and is not empty
dir_exists_and_not_empty() {
    [[ -d "$1" && -n "$(ls -A "$1" 2>/dev/null)" ]]
}

# Function to create backup
create_backup() {
    log INFO "Creating backup of current test infrastructure..."
    
    # Create git branch backup
    git branch "test-cleanup-backup-$(date +%Y%m%d)" 2>/dev/null || log WARN "Git branch backup already exists"
    
    # Create tar backup
    local backup_file="test-infrastructure-backup-$(date +%Y%m%d).tar.gz"
    tar -czf "$backup_file" \
        test_* tests/ test-* 2>/dev/null || log WARN "Some test directories may not exist"
    
    log INFO "Backup created: $backup_file"
}

# Function to run baseline tests
run_baseline_tests() {
    log INFO "Running baseline test coverage..."
    
    # Create baseline reports directory
    mkdir -p "test-reports/baseline"
    
    # Run comprehensive test suite if available
    if [[ -f "scripts/run_all_tests.sh" ]]; then
        log INFO "Running comprehensive test suite..."
        ./scripts/run_all_tests.sh --coverage > "test-reports/baseline/baseline_test_run_$CLEANUP_DATE.log" 2>&1 || {
            log WARN "Some tests failed during baseline run - continuing with cleanup"
        }
    else
        log WARN "Comprehensive test script not found - running individual test commands"
        
        # Run pytest if available
        if command -v pytest >/dev/null 2>&1; then
            pytest tests/ --cov=api --cov-report=html --cov-report=xml \
                --cov-report=json > "test-reports/baseline/pytest_baseline_$CLEANUP_DATE.log" 2>&1 || {
                log WARN "Pytest baseline failed - continuing"
            }
        fi
        
        # Run playwright tests if available
        if [[ -f "package.json" ]] && command -v npx >/dev/null 2>&1; then
            npx playwright test > "test-reports/baseline/playwright_baseline_$CLEANUP_DATE.log" 2>&1 || {
                log WARN "Playwright baseline failed - continuing"
            }
        fi
    fi
    
    log INFO "Baseline testing completed"
}

# Function to create file inventory
create_file_inventory() {
    log INFO "Creating file inventory..."
    
    local inventory_file="test-reports/baseline/file_inventory_$CLEANUP_DATE.txt"
    
    {
        echo "# TurdParty Test Infrastructure File Inventory"
        echo "# Generated: $(date)"
        echo "# Purpose: Document all test-related files before cleanup"
        echo ""
        
        for dir in test_* test-* tests/; do
            if [[ -d "$dir" ]]; then
                echo "## Directory: $dir"
                find "$dir" -type f -exec ls -la {} \; | head -20
                echo "Total files: $(find "$dir" -type f | wc -l)"
                echo ""
            fi
        done
    } > "$inventory_file"
    
    log INFO "File inventory created: $inventory_file"
}

# Phase 1: Assessment and Backup
phase1_assessment() {
    log INFO "=== PHASE 1: Assessment and Backup ==="
    
    cd "$PROJECT_ROOT"
    
    # Create necessary directories
    mkdir -p logs/cleanup test-reports/baseline
    
    # Create backup
    create_backup
    
    # Run baseline tests
    run_baseline_tests
    
    # Create file inventory
    create_file_inventory
    
    log INFO "Phase 1 completed successfully"
}

# Phase 2: Directory Consolidation
phase2_consolidation() {
    log INFO "=== PHASE 2: Directory Consolidation ==="
    
    cd "$PROJECT_ROOT"
    
    # Create new test structure
    log INFO "Creating consolidated test structure..."
    mkdir -p tests/fixtures/{files,data,configs}
    mkdir -p tests/reports/{coverage,performance,screenshots,logs}
    mkdir -p tests/environments/{vm,docker,node}
    
    # Consolidate test fixtures
    log INFO "Consolidating test fixtures..."
    if dir_exists_and_not_empty "test-files"; then
        log INFO "Moving test-files/ to tests/fixtures/files/"
        cp -r test-files/* tests/fixtures/files/ 2>/dev/null || log WARN "No files to move from test-files/"
    fi
    
    if dir_exists_and_not_empty "test_upload_dir"; then
        log INFO "Moving test_upload_dir/ to tests/fixtures/files/"
        cp -r test_upload_dir/* tests/fixtures/files/ 2>/dev/null || log WARN "No files to move from test_upload_dir/"
    fi
    
    # Consolidate reports
    log INFO "Consolidating test reports..."
    if dir_exists_and_not_empty "test-reports"; then
        log INFO "Moving test-reports/ to tests/reports/coverage/"
        cp -r test-reports/* tests/reports/coverage/ 2>/dev/null || log WARN "No files to move from test-reports/"
    fi
    
    if dir_exists_and_not_empty "test_screenshots"; then
        log INFO "Moving test_screenshots/ to tests/reports/screenshots/"
        cp -r test_screenshots/* tests/reports/screenshots/ 2>/dev/null || log WARN "No files to move from test_screenshots/"
    fi
    
    if dir_exists_and_not_empty "test_logs"; then
        log INFO "Moving test_logs/ to tests/reports/logs/"
        cp -r test_logs/* tests/reports/logs/ 2>/dev/null || log WARN "No files to move from test_logs/"
    fi
    
    if dir_exists_and_not_empty "test_results"; then
        log INFO "Moving test_results/ to tests/reports/performance/"
        cp -r test_results/* tests/reports/performance/ 2>/dev/null || log WARN "No files to move from test_results/"
    fi
    
    # Consolidate environments
    log INFO "Consolidating test environments..."
    if dir_exists_and_not_empty "test-dir"; then
        log INFO "Moving test-dir/ to tests/environments/node/"
        cp -r test-dir/* tests/environments/node/ 2>/dev/null || log WARN "No files to move from test-dir/"
    fi
    
    # Handle VM directories (choose the more comprehensive one)
    if dir_exists_and_not_empty "test_vm"; then
        log INFO "Moving test_vm/ to tests/environments/vm/"
        cp -r test_vm/* tests/environments/vm/ 2>/dev/null || log WARN "No files to move from test_vm/"
    fi
    
    if dir_exists_and_not_empty "test-vm"; then
        if [[ ! -d "tests/environments/vm" ]] || [[ -z "$(ls -A tests/environments/vm 2>/dev/null)" ]]; then
            log INFO "Moving test-vm/ to tests/environments/vm/ (primary VM config)"
            cp -r test-vm/* tests/environments/vm/ 2>/dev/null || log WARN "No files to move from test-vm/"
        else
            log INFO "test-vm/ will be archived as duplicate"
        fi
    fi
    
    log INFO "Phase 2 completed successfully"
}

# Phase 3: Archive and Cleanup
phase3_cleanup() {
    log INFO "=== PHASE 3: Archive and Cleanup ==="
    
    cd "$PROJECT_ROOT"
    
    # Create archive directory
    log INFO "Creating archive directory: $ARCHIVE_DIR"
    mkdir -p "$ARCHIVE_DIR"
    
    # Archive old directories
    log INFO "Archiving original test directories..."
    for dir in test_logs test_results test_screenshots test_upload_dir test-dir test-files test-reports test_vm test-vm; do
        if [[ -d "$dir" ]]; then
            log INFO "Archiving $dir/"
            mv "$dir" "$ARCHIVE_DIR/" 2>/dev/null || log WARN "Failed to archive $dir/"
        fi
    done
    
    # Create archive manifest
    log INFO "Creating archive manifest..."
    {
        echo "# Test Infrastructure Cleanup Archive"
        echo "# Date: $(date)"
        echo "# Cleanup PRD: docs/cleanup/test-infrastructure-cleanup-prd.md"
        echo ""
        echo "## Archived Directories:"
        ls -la "$ARCHIVE_DIR/"
        echo ""
        echo "## Archive Reason:"
        echo "Consolidated into tests/ directory structure for better organization"
        echo ""
        echo "## Recovery Instructions:"
        echo "To restore any archived directory:"
        echo "mv _archive_/test-cleanup-$CLEANUP_DATE/[directory] ./"
    } > "$ARCHIVE_DIR/cleanup-manifest.md"
    
    # Update .gitignore
    log INFO "Updating .gitignore..."
    {
        echo ""
        echo "# Test Infrastructure Cleanup - Generated $(date)"
        echo "tests/reports/logs/*.log"
        echo "tests/reports/screenshots/temp_*"
        echo "tests/reports/performance/temp_*"
    } >> .gitignore
    
    log INFO "Phase 3 completed successfully"
}

# Function to run post-cleanup validation
validate_cleanup() {
    log INFO "=== VALIDATION: Post-Cleanup Testing ==="
    
    cd "$PROJECT_ROOT"
    
    # Run tests again to ensure nothing is broken
    if [[ -f "scripts/run_all_tests.sh" ]]; then
        log INFO "Running post-cleanup test validation..."
        ./scripts/run_all_tests.sh > "tests/reports/logs/post_cleanup_validation_$CLEANUP_DATE.log" 2>&1 || {
            log ERROR "Post-cleanup tests failed! Check tests/reports/logs/post_cleanup_validation_$CLEANUP_DATE.log"
            return 1
        }
    else
        log WARN "Comprehensive test script not found - manual validation required"
    fi
    
    # Check directory structure
    log INFO "Validating new directory structure..."
    local expected_dirs=("tests/fixtures" "tests/reports" "tests/environments")
    for dir in "${expected_dirs[@]}"; do
        if [[ -d "$dir" ]]; then
            log INFO "✓ $dir exists"
        else
            log ERROR "✗ $dir missing"
            return 1
        fi
    done
    
    # Count remaining test directories in root
    local remaining_test_dirs=$(find . -maxdepth 1 -name "test*" -type d | wc -l)
    log INFO "Remaining test directories in root: $remaining_test_dirs"
    
    if [[ $remaining_test_dirs -le 1 ]]; then  # Allow tests/ directory
        log INFO "✓ Root directory cleanup successful"
    else
        log WARN "Some test directories remain in root - manual review needed"
    fi
    
    log INFO "Validation completed"
}

# Main execution function
main() {
    local phase=${1:-"all"}
    
    log INFO "Starting TurdParty Test Infrastructure Cleanup"
    log INFO "Phase: $phase"
    log INFO "Date: $CLEANUP_DATE"
    log INFO "Project Root: $PROJECT_ROOT"
    
    case $phase in
        "phase1"|"1")
            phase1_assessment
            ;;
        "phase2"|"2")
            phase2_consolidation
            ;;
        "phase3"|"3")
            phase3_cleanup
            ;;
        "validate")
            validate_cleanup
            ;;
        "all")
            phase1_assessment
            phase2_consolidation
            phase3_cleanup
            validate_cleanup
            ;;
        *)
            echo "Usage: $0 [phase1|phase2|phase3|validate|all]"
            echo ""
            echo "Phases:"
            echo "  phase1   - Assessment and backup"
            echo "  phase2   - Directory consolidation"
            echo "  phase3   - Archive and cleanup"
            echo "  validate - Post-cleanup validation"
            echo "  all      - Run all phases (default)"
            exit 1
            ;;
    esac
    
    log INFO "Test infrastructure cleanup completed successfully!"
    log INFO "Log file: $LOG_FILE"
    log INFO "Archive location: $ARCHIVE_DIR"
}

# Execute main function with all arguments
main "$@"
