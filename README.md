# 🚀 TurdParty API

<div align="center">

![TurdParty Logo](https://img.shields.io/badge/TurdParty-API-blue?style=for-the-badge&logo=fastapi)

[![FastAPI](https://img.shields.io/badge/FastAPI-0.95+-009688?style=flat-square&logo=fastapi&logoColor=white)](https://fastapi.tiangolo.com)
[![Python](https://img.shields.io/badge/Python-3.8+-3776AB?style=flat-square&logo=python&logoColor=white)](https://www.python.org)
[![React](https://img.shields.io/badge/React-18.0+-61DAFB?style=flat-square&logo=react&logoColor=white)](https://reactjs.org)
[![Docker](https://img.shields.io/badge/Docker-Compose-2496ED?style=flat-square&logo=docker&logoColor=white)](https://www.docker.com)
[![MinIO](https://img.shields.io/badge/MinIO-Storage-C72E49?style=flat-square&logo=minio&logoColor=white)](https://min.io)
[![Vagrant](https://img.shields.io/badge/Vagrant-VM-1868F2?style=flat-square&logo=vagrant&logoColor=white)](https://www.vagrantup.com)

*A powerful API for VM management, file handling, and AppImage deployment*

</div>

## 🌟 Features

- **🖥️ VM Management**: Create, start, stop, and manage Vagrant VMs
- **📁 File Handling**: Upload, download, and manage files with MinIO
- **🚀 AppImage Deployment**: Deploy and execute AppImages on VMs
- **🔒 Authentication**: JWT-based authentication with test mode support
- **📊 Monitoring**: Comprehensive logging and performance monitoring
- **🧪 Testing**: Extensive test suite with coverage reporting
- **📚 Documentation**: Comprehensive Sphinx documentation with PDF generation
- **🌐 Multi-language**: Documentation in multiple languages

## 🚀 Quick Start

### 🐳 Using Docker

```bash
# Start the development environment
./.dockerwrapper/dev.sh start

# Access the application
# API: http://localhost:8000
# Frontend: http://localhost:3000
```

### 🧪 Running Tests

Our comprehensive test suite is now organized in a clean, logical structure:

```bash
# Run comprehensive test suite
./docker-scripts/run_all_tests.sh

# Run specific test categories
docker exec turdparty_api pytest tests/api/ -v          # API tests
docker exec turdparty_api pytest tests/integration/ -v  # Integration tests
docker exec turdparty_api pytest tests/unit/ -v         # Unit tests

# Run UI tests with Playwright
npx playwright test tests/playwright/

# Run VM tests
docker exec turdparty_api pytest tests/vm/ -v
```

### 📁 Test Structure

Our tests are organized in a logical hierarchy:

```
tests/
├── fixtures/           # Test data and files
│   ├── files/          # Test files for upload/download
│   ├── data/           # Test datasets
│   └── configs/        # Test configurations
├── reports/            # Test reports and artifacts
│   ├── coverage/       # Coverage reports
│   ├── logs/           # Test execution logs
│   ├── screenshots/    # UI test screenshots
│   └── performance/    # Performance test results
├── environments/       # Test environments
│   ├── vm/             # VM test configurations
│   ├── node/           # Node.js test environment
│   └── docker/         # Docker test configs
├── api/                # API endpoint tests
├── integration/        # Integration tests
├── playwright/         # UI tests with Playwright
├── unit/               # Unit tests
└── vm/                 # VM functionality tests
```

## 🏗️ Architecture

### 🔄 Service Connector Pattern

Type-safe API communication with standardized error handling:

- **ServiceConnector[T]**: Generic base connector class
- **ItemService**: Concrete implementation for item-related endpoints
- **Comprehensive Testing**: Unit tests with mocked responses

### 💉 Dependency Injection

Centralized registry for application services and repositories:

- **Container**: Centralized registry for services
- **Service Resolution**: Automatic dependency resolution
- **Testing Support**: Easy mocking for unit testing

### 📊 Repository Pattern

Generic implementation for all CRUD operations:

- **BaseRepository**: Generic CRUD operations
- **Specific Repositories**: Entity-specific repositories
- **Services Layer**: Business logic implementation

## 🖥️ VM Management

```bash
# List all VMs
curl -X GET "http://localhost:3050/api/v1/vagrant_vm/" -H "Authorization: Bearer YOUR_TOKEN"

# Create a new VM
curl -X POST "http://localhost:3050/api/v1/vagrant_vm/" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "my-vm",
    "template": "ubuntu_20_04",
    "memory_mb": 2048,
    "cpus": 2
  }'

# Execute command on VM
curl -X POST "http://localhost:3050/api/v1/vagrant_vm/{id}/exec" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "command": "echo Hello from VM && hostname"
  }'
```

## 📁 File Upload

The file upload functionality supports:

- Single file uploads
- Multiple file uploads
- Folder uploads
- Progress tracking
- Error handling

### Debugging File Upload

For debugging file upload issues, use the enhanced test suite:

```bash
# Run the debug tests
cd turdparty-app
npx playwright test tests/playwright/file-upload-debug.spec.ts --headed

# Run with verbose logging
npx playwright test tests/playwright/file-upload-debug.spec.ts --headed --debug
```

## 🔒 Authentication

Generate test tokens for development:

```bash
# Generate a regular user token
python scripts/generate_test_token.py

# Generate an admin token
python scripts/generate_test_token.py --admin

# Generate a token for a specific username
python scripts/generate_test_token.py --username john
```

### Test Mode for Authentication Bypass

```bash
# Enable test mode (bypass authentication)
python scripts/toggle_test_mode.py --enable

# Disable test mode (enforce authentication)
python scripts/toggle_test_mode.py --disable
```

## 📊 Dashboards

### Docker Dashboard

Monitor and manage Docker containers:

```bash
# List all containers
./docker-dashboard list

# View logs for a container
./docker-dashboard logs <container_name> --lines <number_of_lines>

# Start all containers
./docker-dashboard up
```

### Cachet Status Page

Monitor service status and manage incidents with Cachet:

```bash
# Start the Cachet status page
./.dockerwrapper/setup-cachet.sh

# Access the status page
# URL: http://localhost:3501
# Default login: <EMAIL>
# Default password: test123
```

For detailed configuration instructions, see [Cachet Configuration Guide](./.dockerwrapper/cachet-configuration-guide.md).

## 📋 Project Milestones

- ✅ **Foundation Setup**: Project structure, database configurations
- ✅ **UI Framework Integration**: Service connector pattern, CRUD operations
- ✅ **Advanced Services**: MinIO storage, Vagrant VM management
- ✅ **🧹 MAJOR: Project Organization**: Comprehensive cleanup and restructuring
  - Consolidated 10+ scattered test directories into organized hierarchy
  - Achieved 90% reduction in root directory clutter
  - Implemented systematic archival with full rollback capability
  - Created automated cleanup scripts and comprehensive documentation
- ✅ **Error Handling and Monitoring**: Error boundaries, UI error logging
- 🔜 **Coming Soon**: Enhanced file upload, extended test coverage

## 🏗️ Project Structure

TurdParty follows a clean, organized structure for maximum maintainability:

```
turdparty/
├── api/                    # FastAPI backend application
├── frontend/               # React frontend application
├── tests/                  # 🆕 Consolidated test suite
│   ├── fixtures/           # Test data and files
│   ├── reports/            # Test reports and artifacts
│   ├── environments/       # Test environments
│   └── [test categories]/  # Organized by test type
├── docs/                   # Comprehensive documentation
│   ├── cleanup/            # 🆕 Cleanup documentation and PRDs
│   ├── api/                # API documentation
│   └── architecture/       # Architecture diagrams
├── config/                 # Configuration files
│   ├── vm/                 # 🆕 VM configurations
│   ├── docker/             # Docker configurations
│   └── [other configs]/    # Environment-specific configs
├── scripts/                # Utility and automation scripts
│   ├── cleanup/            # 🆕 Cleanup automation scripts
│   └── [other scripts]/    # Development and deployment scripts
├── _archive_/              # 🆕 Archived files with full traceability
└── [core files]            # Essential project files
```

### 🎯 Organization Benefits

- **🔍 Easy Navigation**: Clear categorization by purpose and function
- **🧹 Reduced Clutter**: 90% reduction in root directory complexity
- **📚 Better Documentation**: Centralized docs with comprehensive guides
- **🔄 Maintainable**: Systematic approach to file organization
- **🛡️ Safe Changes**: Complete archival system with rollback capability

## 🧪 Test Coverage

Generate and view test coverage reports:

```bash
# Generate test coverage
python scripts/generate_test_coverage.py

# Run tests and report results
python scripts/run_tests_and_report.py

# View HTML coverage report
python3 -m http.server 8000 --directory coverage_reports/html
```

## 🌐 Multi-language Documentation

Documentation is available in multiple languages:

- [Afrikaans (af)](./lang/af/)
- [English (UK) (en_GB)](./lang/en_GB/)
- [German (de)](./lang/de/)
- [Romanian (ro)](./lang/ro/)
- [isiZulu (zu)](./lang/zu/)

## 📚 Documentation

### Sphinx Documentation

TurdParty now includes comprehensive Sphinx documentation with PDF generation support:

```bash
# Build HTML documentation
cd docs
make html

# Build PDF documentation
cd docs
make pdf

# View HTML documentation
open build/html/index.html

# View PDF documentation
open build/pdf/TurdParty_Documentation.pdf
```

Or use the build script:

```bash
./docs/build_docs.sh
```

### Legacy Documentation

- [Vagrant SSH Documentation](docs/vagrant_ssh.md)
- [MinIO Storage Documentation](docs/minio_storage.md)
- [Test Mode Documentation](docs/test_mode.md)
- [Screenshots Documentation](./docs/screenshots/README.md)

## 🛣️ Roadmap

- [x] Implement Cachet as the service status dashboard
- [ ] Dockerwrapper functional and easy to use
- [ ] Translate pages with Deepl API
- [ ] Add user management UI
- [ ] Implement multi-factor authentication
- [ ] Expand MinIO integration with more advanced features
- [ ] Add data visualization components

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.


