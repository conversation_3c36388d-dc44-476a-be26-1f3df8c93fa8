# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|
  # Windows 10 box
  config.vm.box = "gusztavvargadr/windows-10"
  config.vm.box_version = "2102.0.2303"

  # VM name
  config.vm.define "windows10"

  # Communicator settings for Windows
  config.vm.communicator = "winrm"
  config.winrm.username = "vagrant"
  config.winrm.password = "vagrant"
  config.winrm.timeout = 1800

  # Provider-specific configuration
  config.vm.provider "virtualbox" do |vb|
    vb.memory = 4096
    vb.cpus = 2
    vb.gui = false
    vb.customize ["modifyvm", :id, "--vram", "128"]
  end

  # Network configuration
  config.vm.network "private_network", type: "dhcp"

  # Windows-specific provisioning
  config.vm.provision "shell", inline: <<-SHELL
    # Display system information
    systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"
    
    # Create a test file to verify VM is ready
    echo "Windows VM template is ready" > C:\\Users\\<USER>\\Desktop\\vm_ready.txt
  SHELL
end 