# -*- mode: ruby -*-
# vi: set ft=ruby :

Vagrant.configure("2") do |config|
  # Use Windows 10 box from the Vagrant Cloud
  config.vm.box = "gusztavvargadr/windows-10"
  
  # Configure VM resources
  config.vm.provider "virtualbox" do |vb|
    vb.memory = 4096
    vb.cpus = 2
    vb.gui = true
    vb.customize ["modifyvm", :id, "--vram", "128"]
    vb.customize ["modifyvm", :id, "--clipboard", "bidirectional"]
    vb.customize ["modifyvm", :id, "--draganddrop", "bidirectional"]
  end

  # Configure network
  config.vm.network "private_network", type: "dhcp"
  
  # Configure WinRM for communication
  config.vm.communicator = "winrm"
  config.winrm.username = "vagrant"
  config.winrm.password = "vagrant"
  config.winrm.timeout = 1800
  
  # Windows-specific customization
  config.vm.provision "shell", inline: <<-SHELL
    # Display system information
    systeminfo | findstr /B /C:"OS Name" /C:"OS Version" /C:"System Type"
    
    # Install Chocolatey package manager
    Set-ExecutionPolicy Bypass -Scope Process -Force
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
    iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))
    
    # Install basic tools
    choco install -y git 7zip
    
    # Create a test file on desktop
    echo "Windows VM is ready for testing" > C:\\Users\\<USER>\\Desktop\\vm_ready.txt
  SHELL
end 