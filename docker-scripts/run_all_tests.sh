#!/bin/bash

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Running all tests for TurdParty${NC}"
echo -e "${YELLOW}===============================${NC}"

# Ensure MinIO is running on host machine
if ! command -v minio &> /dev/null; then
    echo -e "${RED}ERROR: MinIO is not installed on the host.${NC}"
    echo -e "Please install MinIO first using:"
    echo -e "wget https://dl.min.io/server/minio/release/linux-amd64/minio"
    echo -e "chmod +x minio"
    echo -e "sudo mv minio /usr/local/bin/"
    exit 1
fi

# Check if MinIO is running
if ! pgrep -x "minio" > /dev/null; then
    echo -e "${YELLOW}Starting MinIO on the host...${NC}"
    mkdir -p ~/minio-data
    nohup minio server ~/minio-data --console-address ":9001" > ~/minio.log 2>&1 &
    sleep 3
    echo -e "${GREEN}MinIO started on port 9000 with console on 9001${NC}"
fi

# Services are already running, just verify they're healthy
echo -e "${YELLOW}Verifying services are running...${NC}"
if ! docker ps | grep -q "turdparty_api"; then
    echo -e "${RED}TurdParty API container not running${NC}"
    exit 1
fi

echo -e "${GREEN}All services are healthy, proceeding with tests...${NC}"

# Test our new organized test structure
echo -e "${YELLOW}Running tests from organized test structure...${NC}"

echo -e "${YELLOW}Running API tests...${NC}"
docker exec turdparty_api python -m pytest tests/api/ -v --tb=short || echo -e "${RED}API tests failed${NC}"

echo -e "${YELLOW}Running integration tests...${NC}"
docker exec turdparty_api python -m pytest tests/integration/ -v --tb=short || echo -e "${RED}Integration tests failed${NC}"

echo -e "${YELLOW}Running unit tests...${NC}"
docker exec turdparty_api python -m pytest tests/unit/ -v --tb=short || echo -e "${RED}Unit tests failed${NC}"

echo -e "${YELLOW}Running VM tests...${NC}"
docker exec turdparty_api python -m pytest tests/vm/ -v --tb=short || echo -e "${RED}VM tests failed${NC}"

echo -e "${YELLOW}Running MinIO tests...${NC}"
docker exec turdparty_api python -m pytest tests/minio/ -v --tb=short || echo -e "${RED}MinIO tests failed${NC}"

# Test basic functionality
echo -e "${YELLOW}Testing basic API functionality...${NC}"
if curl -s http://localhost:3050/api/v1/health | grep -q "ok"; then
    echo -e "${GREEN}✓ API health check passed${NC}"
else
    echo -e "${RED}✗ API health check failed${NC}"
fi

if curl -s http://localhost:3100 | grep -q "TurdParty"; then
    echo -e "${GREEN}✓ Frontend is accessible${NC}"
else
    echo -e "${RED}✗ Frontend is not accessible${NC}"
fi

# Check if Playwright test should be run
echo -e "${YELLOW}Running UI tests with Playwright...${NC}"
if [ -f "./scripts/run-playwright-tests.sh" ]; then
    bash ./scripts/run-playwright-tests.sh
else
    echo -e "${RED}Playwright test script not found.${NC}"
fi

echo -e "${YELLOW}Running API test script...${NC}"
bash ./test-api.sh

echo -e "${GREEN}All tests completed!${NC}" 