Welcome to TurdParty Documentation
===============================

.. image:: _static/logo.png
   :alt: TurdParty Logo
   :align: center
   :width: 200px

TurdParty is a production-ready microservices application for managing Vagrant virtual machines, file uploads, MinIO storage, and service monitoring with integrated status dashboards.

.. note::
   **🎉 MAJOR UPDATE: Project Organization Complete!** TurdParty has undergone comprehensive cleanup and restructuring:

   - **🧹 Test Infrastructure**: Consolidated 10+ scattered test directories into organized hierarchy
   - **📁 Root Directory**: Achieved 90% reduction in root directory clutter
   - **🗃️ Systematic Archival**: Complete backup system with full rollback capability
   - **🤖 Automation**: Created comprehensive cleanup scripts and documentation
   - **📚 Documentation**: Enhanced with cleanup PRDs and validation reports

   The project now follows production-grade organization standards with clear categorization and maintainable structure.

.. toctree::
   :maxdepth: 2
   :caption: Contents:

   getting-started/index
   architecture/index
   api/index
   development/index
   deployment/index
   troubleshooting/index
   user-guides/index
   reference/index
   changelog

.. toctree::
   :maxdepth: 2
   :caption: Production Features:

   production/index

Features
--------

Core Application Features
~~~~~~~~~~~~~~~~~~~~~~~~~

- **Vagrant VM Management**: Create, manage, and monitor Vagrant virtual machines
- **File Upload & Storage**: Upload and manage files with MinIO object storage
- **VM Injection**: Inject files into Vagrant VMs via SSH or gRPC
- **Static Analysis**: Analyze files for security and quality
- **Service Monitoring**: Real-time service status with Cachet dashboard
- **Async Task Processing**: Background task processing with Celery workers
- **API Integration**: Comprehensive REST API for automation and integration
- **Container Orchestration**: Docker-based microservices architecture

Production-Ready Features
~~~~~~~~~~~~~~~~~~~~~~~~~

- **🧹 MAJOR: Project Organization**: Comprehensive cleanup and restructuring initiative
  - Consolidated 10+ scattered test directories into organized hierarchy
  - Achieved 90% reduction in root directory clutter
  - Implemented systematic archival with full rollback capability
  - Created automated cleanup scripts and comprehensive documentation
- **🏠 Local CI/CD Pipeline**: 9-stage comprehensive testing pipeline (alternative to GitHub Actions)
- **🏥 Health Monitoring**: 40+ automated health checks for all services
- **🔧 Pre-commit Hooks**: Automated code quality and security checks
- **📁 Clean Project Structure**: Organized file structure with professional categorization
- **🛡️ Security Scanning**: Automated sensitive data detection and Docker security validation
- **⚡ Performance Monitoring**: Response time tracking and resource usage monitoring
- **🐳 Namespaced Containers**: All containers use `turdparty_` prefix for isolation
- **📊 Comprehensive Testing**: Integration, API, database, and storage testing with organized structure

About This Documentation
-----------------------

This documentation is built using Sphinx and includes:

- Installation and configuration guides
- Architecture documentation
- API reference
- Development guides
- Troubleshooting information
- User guides
- Reference materials

The documentation is available in HTML format for online viewing and PDF format for offline reference.

Getting Started
--------------

Check out the :doc:`getting-started/index` section for information on how to install and use TurdParty.

Development
----------

For developers, the :doc:`development/index` section provides information on contributing to the project,
testing procedures, and the development roadmap.

Architecture
-----------

The :doc:`architecture/index` section provides detailed information about the system architecture,
including Vagrant VM management, MinIO storage, and the FastAPI application infrastructure.

Indices and Tables
-----------------

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
