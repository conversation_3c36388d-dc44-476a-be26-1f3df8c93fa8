Python Test Summary
===================
Run at: Tue  8 Apr 14:55:16 CEST 2025

| Test File | Status | Notes |
|-----------|--------|-------|
| ./test_minio.py | ✓ PASS | |
| ./vm_exec_test.py | ✗ FAIL |  |
| ./scripts/test_minio.py | ✗ FAIL |  |
| ./scripts/test_virustotal.py | ✗ FAIL |  |
| ./scripts/test_model_integration.py | ✗ FAIL |  |
| ./scripts/test_grpc_connection.py | ✗ FAIL |  |
| ./scripts/test_upload_to_vm.py | ✗ FAIL |  |
| ./scripts/test_fathom_api.py | ✗ FAIL |  |
| ./scripts/test_hash_caching.py | ✗ FAIL |  |
| ./scripts/test_vm_templates.py | ✓ PASS | |
| ./scripts/test_vagrant_connection.py | ✗ FAIL |  |
| ./scripts/test_fathom_remote.py | ✗ FAIL |  |
| ./scripts/test_ssh_key.py | ✗ FAIL |  |
| ./scripts/test_minio_settings.py | ✗ FAIL |  |
| ./scripts/test_key_format.py | ✗ FAIL |  |
| ./scripts/test_vagrant_remote_status.py | ✗ FAIL |  |
| ./scripts/test_minio_integration.py | ✗ FAIL |  |
| ./scripts/test_file_upload_paths.py | ✓ PASS | |
| ./scripts/test_minio_ssh.py | ✗ FAIL |  |
| ./scripts/test_fathom_connection.py | ✗ FAIL |  |
| ./scripts/test_vagrant_ssh.py | ✗ FAIL |  |
| ./test_item_model.py | ✓ PASS | |
| ./test_routes.py | ✗ FAIL |  |
| ./standalone_route_test.py | ✗ FAIL |  |
| ./api/tests/test_vagrant_client.py | ✗ FAIL |  |
| ./api/tests/test_real_server.py | ✗ FAIL |  |
| ./api/tests/test_vagrant_service.py | ✗ FAIL |  |
| ./api/tests/test_fathom_ssh_client.py | ✗ FAIL |  |
| ./api/tests/test_error_handling.py | ✗ FAIL |  |
| ./api/tests/test_vagrant_endpoints.py | ✗ FAIL |  |
| ./api/tests/test_repositories.py | ✗ FAIL |  |
