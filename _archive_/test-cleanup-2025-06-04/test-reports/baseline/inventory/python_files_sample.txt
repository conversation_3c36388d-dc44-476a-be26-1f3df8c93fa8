./test_minio.py
./.focus_forge/run_analytics_tests.py
./.focus_forge/__init__.py
./.focus_forge/visualize_web.py
./.focus_forge/test_task_validation.py
./.focus_forge/run_ai_folder_tests.py
./.focus_forge/test_subtasks_fix.py
./.focus_forge/tests/test_utils.py
./.focus_forge/tests/test_task_management_adapter.py
./.focus_forge/tests/test_learning_system.py
./.focus_forge/tests/test_integration_functions.py
./.focus_forge/tests/test_focus_forge_integration.py
./.focus_forge/tests/test_wakatime_cli.py
./.focus_forge/tests/test_task_upgrader.py
./.focus_forge/tests/test_timer_cli.py
./.focus_forge/tests/test_dependency_graph.py
./.focus_forge/tests/test_doc_discovery.py
./.focus_forge/tests/test_sequence_analysis_extended.py
./.focus_forge/tests/test_pomodoro.py
./.focus_forge/tests/test_critical_path.py
./.focus_forge/tests/test_nlp_relationship_detection.py
./.focus_forge/tests/test_butterbot.py
./.focus_forge/tests/test_edge_cases.py
./.focus_forge/tests/test_sequence_constraints.py
./.focus_forge/tests/test_wakatime_integration.py
./.focus_forge/tests/test_api_retry.py
./.focus_forge/tests/test_integration.py
./.focus_forge/tests/test_component_detection.py
./.focus_forge/tests/test_api.py
./.focus_forge/tests/test_comprehensive.py
./.focus_forge/tests/test_cli_integration.py
./.focus_forge/tests/test_pomodoro_cli.py
./.focus_forge/tests/test_timer.py
./.focus_forge/tests/conftest.py
./.focus_forge/tests/test_cli.py
./.focus_forge/tests/test_api_cost.py
./.focus_forge/tests/test_graph_visualization.py
./.focus_forge/tests/test_sequence_cli.py
./.focus_forge/tests/test_dependency_graph_minimal.py
./.focus_forge/test_task_generation_prd.py
./.focus_forge/visualize.py
./.focus_forge/setup.py
./.focus_forge/verify_tasks_location.py
./.focus_forge/run_estimation_tests.py
./.focus_forge/focus_forge.py
./.focus_forge/test_submodule_path.py
./.focus_forge/simple_test.py
./.focus_forge/test_task_timer_integration.py
./.focus_forge/verify_modules.py
./.focus_forge/test_edge_cases.py
./.focus_forge/example_usage.py
./.focus_forge/confidence_scoring_integration.py
./.focus_forge/test_timer_direct.py
./.focus_forge/test_integration.py
./.focus_forge/update_task_status.py
./.focus_forge/test_api_retry_direct.py
./.focus_forge/test_confidence_scoring.py
./.focus_forge/focus_forge/pomodoro.py
./.focus_forge/focus_forge/__init__.py
./.focus_forge/focus_forge/task_generation.py
./.focus_forge/focus_forge/analytics_visualization.py
./.focus_forge/focus_forge/analyze_prd.py
./.focus_forge/focus_forge/sequence_analysis.py
./.focus_forge/focus_forge/component_detection.py
./.focus_forge/focus_forge/pomodoro_cli_integration.py
./.focus_forge/focus_forge/cli_integration.py
./.focus_forge/focus_forge/task_upgrader.py
./.focus_forge/focus_forge/tests/test_utils.py
./.focus_forge/focus_forge/tests/integration_test.py
./.focus_forge/focus_forge/tests/test_task_generation.py
./.focus_forge/focus_forge/tests/test_estimation_edge_cases.py
./.focus_forge/focus_forge/tests/test_priority_inference.py
./.focus_forge/focus_forge/tests/test_analytics_visualization.py
./.focus_forge/focus_forge/tests/test_estimation_integration.py
./.focus_forge/focus_forge/tests/test_task_file_formats.py
./.focus_forge/focus_forge/tests/test_edge_cases.py
./.focus_forge/focus_forge/tests/test_integration.py
./.focus_forge/focus_forge/tests/test_ai_folder_integration.py
./.focus_forge/focus_forge/tests/test_api.py
./.focus_forge/focus_forge/tests/test_estimation.py
./.focus_forge/focus_forge/tests/test_analytics.py
./.focus_forge/focus_forge/tests/test_cli.py
./.focus_forge/focus_forge/tests/test_estimation_performance.py
./.focus_forge/focus_forge/graph_visualization_cli.py
./.focus_forge/focus_forge/setup.py
./.focus_forge/focus_forge/task_validation.py
./.focus_forge/focus_forge/estimation.py
./.focus_forge/focus_forge/doc_discovery.py
./.focus_forge/focus_forge/nlp.py
./.focus_forge/focus_forge/focus_forge.py
./.focus_forge/focus_forge/api_cost_cli.py
./.focus_forge/focus_forge/graph_visualization.py
./.focus_forge/focus_forge/cli_upgrader.py
./.focus_forge/focus_forge/simple_task_reader.py
./.focus_forge/focus_forge/timer.py
./.focus_forge/focus_forge/priority_inference.py
./.focus_forge/focus_forge/analytics.py
./.focus_forge/focus_forge/estimation_cli.py
./.focus_forge/focus_forge/task_management_integration.py
./.focus_forge/focus_forge/cli/graph_commands.py
