-rw-r--r-- 1 <USER> <GROUP> 24K May 14 17:16 ./vm_status.png
-rw-r--r-- 1 <USER> <GROUP> 2.7K May 14 17:16 ./test_minio.py
-rwxr-xr-x 1 <USER> <GROUP> 1.8K May 14 19:24 ./run-celery-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 126 May 14 17:16 ./test-api-upload.txt
-rw-r--r-- 1 <USER> <GROUP> 1.9K May 14 17:16 ./check_swagger_ui.js
-rw-r--r-- 1 <USER> <GROUP> 342 May 14 17:16 ./.coveragerc
-rw-r--r-- 1 <USER> <GROUP> 619 May 14 17:16 ./ui_test.js
-rw-r--r-- 1 <USER> <GROUP> 3.3K May 14 17:52 ./docker-compose.yml
-rwxr-xr-x 1 <USER> <GROUP> 15K May 14 17:16 ./run-comprehensive-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 244 Jun  3 23:43 ./cleanup_master_20250603_234345.log
-rw-r--r-- 1 <USER> <GROUP> 37K May 14 17:16 ./target_model_state.md
-rwxr-xr-x 1 <USER> <GROUP> 527 May 14 17:16 ./test-upload.sh
-rw-r--r-- 1 <USER> <GROUP> 5.0K May 14 17:16 ./test-e2e-upload-container.js
-rwxr-xr-x 1 <USER> <GROUP> 5.9K May 14 17:16 ./run-specific-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 1.9K Jun  3 23:45 ./cleanup_master_20250603_234530.log
-rwxr-xr-x 1 <USER> <GROUP> 4.7K May 14 17:16 ./run_tests.py
-rw-r--r-- 1 <USER> <GROUP> 3.9K May 15 14:14 ./AI_SCANNER_README.md
-rwxr-xr-x 1 <USER> <GROUP> 15K May 14 17:16 ./vm_exec_test.py
-rw-r--r-- 1 <USER> <GROUP> 8.4K May 14 17:16 ./api_test_server.py
-rwxr-xr-x 1 <USER> <GROUP> 11K May 14 17:16 ./run_test_analysis.py
-rwxr-xr-x 1 <USER> <GROUP> 354 May 14 17:16 ./test-upload-fixed.sh
-rwxr-xr-x 1 <USER> <GROUP> 14K May 15 14:37 ./scan_ai_references.py
-rwxr-xr-x 1 <USER> <GROUP> 935 May 14 17:16 ./setup_minio_env.sh
-rw-r--r-- 1 <USER> <GROUP> 1.8K May 14 17:16 ./final-test.js
-rw-r--r-- 1 <USER> <GROUP> 285K May 14 17:16 ./generated-icon.png
-rw-r--r-- 1 <USER> <GROUP> 672 May 14 17:16 ./file_upload_page.html
-rw-r--r-- 1 <USER> <GROUP> 1.1K May 14 17:16 ./file_upload_test_status.txt
-rw-r--r-- 1 <USER> <GROUP> 2.2K May 15 11:32 ./debug_openapi.py
-rw-r--r-- 1 <USER> <GROUP> 3.7K May 14 17:16 ./test_minio_fixed.py
-rw-r--r-- 1 <USER> <GROUP> 4.5K May 15 10:28 ./MIGRATION.md
-rw-r--r-- 1 <USER> <GROUP> 22K May 14 17:16 ./tasks.json
-rw-r--r-- 1 <USER> <GROUP> 5.3K May 14 17:16 ./create_ui_links.sh
-rwxr-xr-x 1 <USER> <GROUP> 4.7K May 14 17:16 ./generate_endpoint_test_data.py
-rw-r--r-- 1 <USER> <GROUP> 6.8K May 14 17:16 ./test-file-upload-ui.js
-rw-r--r-- 1 <USER> <GROUP> 33 May 14 17:16 ./playwright-auth.json
-rwxr-xr-x 1 <USER> <GROUP> 627 May 14 17:16 ./run-vm-ui-tests.sh
-rwxr-xr-x 1 <USER> <GROUP> 23K May 14 17:16 ./docker-standalone-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 2.1K May 14 17:16 ./alembic.ini
-rw-r--r-- 1 <USER> <GROUP> 428 May 14 17:16 ./Dockerfile
-rw-r--r-- 1 <USER> <GROUP> 5.5K May 14 17:16 ./file_upload.png
-rw-r--r-- 1 <USER> <GROUP> 7.1K May 14 17:16 ./textual_ui.py
-rw-r--r-- 1 <USER> <GROUP> 5.5K May 14 17:16 ./test_item_model.py
-rwxr-xr-x 1 <USER> <GROUP> 8.1K May 14 17:16 ./analyze_coverage.py
-rw-r--r-- 1 <USER> <GROUP> 2.6K May 14 17:16 ./test_results.md
-rwxr-xr-x 1 <USER> <GROUP> 1.3K May 14 17:16 ./debug_vm_exec.sh
-rwxr-xr-x 1 <USER> <GROUP> 3.0K May 14 17:16 ./test-folder-upload.sh
-rwxr-xr-x 1 <USER> <GROUP> 11K May 14 17:16 ./test_vm_e2e.py
-rwxr-xr-x 1 <USER> <GROUP> 6.8K May 14 17:16 ./test-vm-injection-modes.sh
-rwxr-xr-x 1 <USER> <GROUP> 3.3K May 14 22:31 ./run_tests_with_logs.sh
-rw-r--r-- 1 <USER> <GROUP> 1007 May 14 17:16 ./debug_file_upload.py
-rw-r--r-- 1 <USER> <GROUP> 199 May 14 17:16 ./test.spec.js
-rw-r--r-- 1 <USER> <GROUP> 704 Jun  3 23:43 ./cleanup_master_20250603_234339.log
-rw-r--r-- 1 <USER> <GROUP> 4.2K May 14 17:16 ./test-api-reachable.py
-rwxr-xr-x 1 <USER> <GROUP> 5.0K May 14 17:16 ./test_routes.py
-rw-r--r-- 1 <USER> <GROUP> 3.9K May 15 14:13 ./ai_scanner_README.md
-rw-r--r-- 1 <USER> <GROUP> 1.2K May 14 17:16 ./main.py
-rw-r--r-- 1 <USER> <GROUP> 32 May 14 17:16 ./test_upload.txt
-rwxr-xr-x 1 <USER> <GROUP> 6.3K May 14 17:16 ./run-python-tests-volume.sh
-rwxr-xr-x 1 <USER> <GROUP> 479 May 14 17:16 ./run_vm_tests.sh
-rw-r--r-- 1 <USER> <GROUP> 5.1K May 14 17:16 ./file-upload-standalone.js
-rw-r--r-- 1 <USER> <GROUP> 2.5K May 14 17:16 ./inject_file_scp.sh
-rw-r--r-- 1 <USER> <GROUP> 18 May 14 17:16 ./testfile.txt
-rw-r--r-- 1 <USER> <GROUP> 44K Jun  3 23:44 ./cleanup_master_20250603_234407.log
-rw-r--r-- 1 <USER> <GROUP> 1.8K May 14 22:15 ./shell.nix
-rwxr-xr-x 1 <USER> <GROUP> 667 May 14 17:16 ./rebuild_containers.sh
-rw-r--r-- 1 <USER> <GROUP> 462 May 14 17:16 ./Dockerfile.diagnostic
-rw-r--r-- 1 <USER> <GROUP> 8.9K Jun  3 23:39 ./CLEANUP_IMPLEMENTATION_GUIDE.md
-rwxr-xr-x 1 <USER> <GROUP> 1.8K May 14 17:16 ./run_test.sh
-rw-r--r-- 1 <USER> <GROUP> 6.4K May 14 17:16 ./file-upload-testing.md
-rwxr-xr-x 1 <USER> <GROUP> 14K May 14 17:16 ./run-ui-services-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 7.1K May 15 18:41 ./README.md
-rw-r--r-- 1 <USER> <GROUP> 884 May 14 17:16 ./ui_components_test.js
-rwxr-xr-x 1 <USER> <GROUP> 14K May 14 17:16 ./dashboard.py
-rwxr-xr-x 1 <USER> <GROUP> 15K May 14 17:16 ./standalone_route_test.py
-rw-r--r-- 1 <USER> <GROUP> 2.5K May 14 17:16 ./DASHBOARD.md
-rw-r--r-- 1 <USER> <GROUP> 527K May 15 19:09 ./db_response.html
-rwxr-xr-x 1 <USER> <GROUP> 718 May 14 17:16 ./start-vagrant-proxy.sh
-rw-r--r-- 1 <USER> <GROUP> 2.8K May 14 17:16 ./check_docs_endpoint.js
-rwxr-xr-x 1 <USER> <GROUP> 5.2K May 14 17:16 ./drop_test_index.py
-rwxr-xr-x 1 <USER> <GROUP> 9.7K May 14 17:16 ./test-vm-injection-api.py
-rw-r--r-- 1 <USER> <GROUP> 934 May 15 10:26 ./.gitignore
-rw-r--r-- 1 <USER> <GROUP> 606 May 14 17:16 ./server.js
-rw-r--r-- 1 <USER> <GROUP> 348K May 15 19:09 ./app_response.html
-rw-r--r-- 1 <USER> <GROUP> 6.5K May 14 17:16 ./update_templates.py
-rwxr-xr-x 1 <USER> <GROUP> 7.2K May 14 17:16 ./run_isolated_parallel_tests.sh
-rwxr-xr-x 1 <USER> <GROUP> 3.3K May 14 17:16 ./run-docker-playwright-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 22K May 14 17:16 ./model_analysis.md
-rw-r--r-- 1 <USER> <GROUP> 6.3K May 14 17:16 ./test-api-upload.sh
-rw-r--r-- 1 <USER> <GROUP> 13K May 15 17:55 ./project_structure_prd.md
-rwxr-xr-x 1 <USER> <GROUP> 12K May 14 17:16 ./test-appimage-via-api.sh
-rw-r--r-- 1 <USER> <GROUP> 3.3K May 15 18:15 ./cachet-implementation-summary.md
-rwxr-xr-x 1 <USER> <GROUP> 564 May 14 17:16 ./run-api-integration-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 1.2K May 14 17:16 ./docker-compose.diagnostic.yml
-rwxr-xr-x 1 <USER> <GROUP> 1.2K May 14 17:16 ./run-service-monitor.sh
-rw-r--r-- 1 <USER> <GROUP> 4.0K May 14 17:16 ./register_windows_template.py
-rwxr-xr-x 1 <USER> <GROUP> 16K May 14 17:16 ./simple_vm_test.py
-rw-r--r-- 1 <USER> <GROUP> 34K May 14 17:16 ./file-upload-page.png
-rw-r--r-- 1 <USER> <GROUP> 11K May 14 17:16 ./test-api-upload.js
-rwxr-xr-x 1 <USER> <GROUP> 6.0K May 14 17:16 ./run_playwright_tests.sh
-rw-r--r-- 1 <USER> <GROUP> 7.3K May 14 17:16 ./api-frontend-alignment.md
-rwxr-xr-x 1 <USER> <GROUP> 1.6K May 14 17:16 ./run_migrations.py
-rwxr-xr-x 1 <USER> <GROUP> 7.7K May 14 17:16 ./run-tests-with-volumes.sh
-rw-r--r-- 1 <USER> <GROUP> 2.6K May 14 17:16 ./check_docs_content.js
-rwxr-xr-x 1 <USER> <GROUP> 1.3K May 14 17:16 ./run-single-test.sh
-rw-r--r-- 1 <USER> <GROUP> 18 May 14 17:16 ./test_file.txt
-rw-r--r-- 1 <USER> <GROUP> 142 May 14 17:16 ./requirements-test.txt
-rw-r--r-- 1 <USER> <GROUP> 533 May 14 17:16 ./minimal-config.js
-rw-r--r-- 1 <USER> <GROUP> 7.7K May 14 17:16 ./minio_ssh_wrapper_fixed.py
-rw-r--r-- 1 <USER> <GROUP> 8.7K Jun  3 23:31 ./folder-structure-cleanup-prd.md
-rwxr-xr-x 1 <USER> <GROUP> 4.1K May 14 17:16 ./test_vm_injection_service.py
-rwxr-xr-x 1 <USER> <GROUP> 2.7K May 14 17:16 ./docker-run-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 147K May 15 19:09 ./setup_response.html
-rw-r--r-- 1 <USER> <GROUP> 3.1K May 14 17:16 ./upload-status-diagram.md
-rw-r--r-- 1 <USER> <GROUP> 5.1K May 14 17:16 ./prd.md
-rwxr-xr-x 1 <USER> <GROUP> 4.1K May 14 17:16 ./run_tests_without_auth.py
-rw-r--r-- 1 <USER> <GROUP> 197 May 14 17:16 ./Dockerfile.simple
-rw-r--r-- 1 <USER> <GROUP> 137K May 14 17:16 ./poetry.lock
-rwxr-xr-x 1 <USER> <GROUP> 2.8K May 14 17:16 ./add_test_user.py
-rw-r--r-- 1 <USER> <GROUP> 2.7K May 14 17:16 ./ROADMAP.md
-rw-r--r-- 1 <USER> <GROUP> 672 May 14 17:16 ./file_upload_page_after.html
-rwxr-xr-x 1 <USER> <GROUP> 1.7K May 14 17:16 ./push-docker-images.sh
-rwxr-xr-x 1 <USER> <GROUP> 972 May 14 17:16 ./run-vm-injection-test.sh
-rw-r--r-- 1 <USER> <GROUP> 3.9K May 14 17:16 ./test_vm_injection_ui.js
-rw-r--r-- 1 <USER> <GROUP> 910 May 14 17:16 ./docker-compose.test.yml
-rw-r--r-- 1 <USER> <GROUP> 2.0K May 14 17:16 ./init_db.py
-rwxr-xr-x 1 <USER> <GROUP> 8.6K May 14 17:16 ./identify-streamlit-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 1.1K May 14 17:48 ./requirements.txt
-rw-r--r-- 1 <USER> <GROUP> 12K May 14 17:16 ./enhanced-upload-test.js
-rw-r--r-- 1 <USER> <GROUP> 798 May 14 17:16 ./pytest.ini
-rw-r--r-- 1 <USER> <GROUP> 616 May 14 17:16 ./create_user.py
-rw-r--r-- 1 <USER> <GROUP> 2.3K May 14 17:16 ./check_openapi_and_ui.js
-rwxr-xr-x 1 <USER> <GROUP> 1.7K May 14 17:16 ./run_api_test.py
-rw-r--r-- 1 <USER> <GROUP> 1.2K May 14 17:16 ./Vagrantfile
-rw-r--r-- 1 <USER> <GROUP> 1.2K May 14 17:16 ./test_redirect.py
-rw-r--r-- 1 <USER> <GROUP> 1.7K May 14 17:16 ./playwright.config.js
-rw-r--r-- 1 <USER> <GROUP> 2.8K May 14 17:16 ./SUMMARY.md
-rwxr-xr-x 1 <USER> <GROUP> 7.6K May 14 17:16 ./test-direct-appimage-minio.sh
-rw-r--r-- 1 <USER> <GROUP> 848 May 14 17:16 ./Dockerfile.testing
-rwxr-xr-x 1 <USER> <GROUP> 4.7K May 14 17:16 ./test-host-minio.sh
-rwxr-xr-x 1 <USER> <GROUP> 1.4K May 14 17:16 ./diagnose_test.py
-rwxr-xr-x 1 <USER> <GROUP> 351 May 14 17:16 ./test-upload-latest.sh
-rwxr-xr-x 1 <USER> <GROUP> 790 May 14 17:16 ./install-dashboard-deps.sh
-rwxr-xr-x 1 <USER> <GROUP> 2.0K May 14 17:16 ./run_python_tests.sh
-rwxr-xr-x 1 <USER> <GROUP> 1.8K May 14 17:16 ./test_uploader.py
-rw-r--r-- 1 <USER> <GROUP> 1.2K May 14 17:16 ./package.json
-rwxr-xr-x 1 <USER> <GROUP> 6.5K May 14 17:16 ./test-vm-injection-ssh.sh
-rwxr-xr-x 1 <USER> <GROUP> 3.2K May 14 17:16 ./run-functional-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 4.6K May 14 17:16 ./test-e2e-upload.js
-rw-r--r-- 1 <USER> <GROUP> 9.0K May 14 17:16 ./db_tasks.py
-rw-r--r-- 1 <USER> <GROUP> 936 May 14 17:16 ./basic-test.js
-rw-r--r-- 1 <USER> <GROUP> 116K May 14 17:16 ./test.db
-rwxr-xr-x 1 <USER> <GROUP> 5.4K May 14 17:16 ./run-streamlit-audit.sh
-rwxr-xr-x 1 <USER> <GROUP> 4.2K May 14 17:16 ./temp_vm_test.py
-rwxr-xr-x 1 <USER> <GROUP> 4.2K May 14 17:16 ./run_tests_in_docker.py
-rwxr-xr-x 1 <USER> <GROUP> 5.1K May 14 17:16 ./run_parallel_tests.sh
-rwxr-xr-x 1 <USER> <GROUP> 3.0K May 14 17:16 ./check_table.py
-rw-r--r-- 1 <USER> <GROUP> 1.9K May 14 17:16 ./DOCKER.md
-rw-r--r-- 1 <USER> <GROUP> 966 May 14 17:16 ./Dockerfile.test
-rw-r--r-- 1 <USER> <GROUP> 3.4K May 14 17:16 ./upload-test-summary.md
-rw-r--r-- 1 <USER> <GROUP> 4.8K May 14 17:16 ./windows_test.py
-rwxr-xr-x 1 <USER> <GROUP> 3.5K May 14 17:16 ./test-minio-upload.sh
-rwxr-xr-x 1 <USER> <GROUP> 707 May 14 17:16 ./run-docker-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 39K May 14 17:16 ./playwright-test-plan.md
-rw-r--r-- 1 <USER> <GROUP> 2.6K May 14 17:16 ./openapi_proxy.py
-rwxr-xr-x 1 <USER> <GROUP> 2.1K May 14 17:16 ./run_ui_tests.sh
-rw-r--r-- 1 <USER> <GROUP> 17K May 14 17:16 ./windows_template_test.py
-rw-r--r-- 1 <USER> <GROUP> 3.9K May 14 17:16 ./file-upload-e2e-status.md
-rw-r--r-- 1 <USER> <GROUP> 586 May 14 17:16 ./.dockerignore
-rw-r--r-- 1 <USER> <GROUP> 2.3K May 14 17:16 ./check_basic_openapi.js
-rw-r--r-- 1 <USER> <GROUP> 7.7K May 14 17:16 ./remediation-plan.md
-rw-r--r-- 1 <USER> <GROUP> 12K May 14 17:16 ./test_plan.md
-rwxr-xr-x 1 <USER> <GROUP> 671 May 14 17:16 ./run_consolidated_vm_tests.sh
-rw-r--r-- 1 <USER> <GROUP> 621 May 14 17:16 ./service-monitor-shell.nix
-rwxr-xr-x 1 <USER> <GROUP> 5.7K May 14 17:16 ./run-tests-directly.js
-rwxr-xr-x 1 <USER> <GROUP> 3.7K May 14 17:16 ./database_init_alembic.py
-rw-r--r-- 1 <USER> <GROUP> 682 May 14 17:16 ./turdparty.code-workspace
-rwxr-xr-x 1 <USER> <GROUP> 5.4K May 14 17:16 ./run-all-upload-tests.sh
-rw-r--r-- 1 <USER> <GROUP> 3.4K May 14 17:16 ./upload-testing-findings.md
-rw-r--r-- 1 <USER> <GROUP> 543 May 15 19:09 ./cache_response.html
-rw-r--r-- 1 <USER> <GROUP> 2.0K May 14 17:16 ./docker-compose.testing.yml
-rw-r--r-- 1 <USER> <GROUP> 8.1K May 15 18:10 ./cachet-prd.md
-rwxr-xr-x 1 <USER> <GROUP> 3.2K May 14 17:16 ./enable_test_mode_in_application.py
-rwxr-xr-x 1 <USER> <GROUP> 1.8K May 14 17:16 ./run_api_coverage.py
-rwxr-xr-x 1 <USER> <GROUP> 1.5K May 14 17:16 ./test-folder-upload-simple.sh
-rw-r--r-- 1 <USER> <GROUP> 1.5K May 14 17:16 ./timer_data.json
-rwxr-xr-x 1 <USER> <GROUP> 1.5K May 14 17:16 ./test-file-upload.sh
-rw-r--r-- 1 <USER> <GROUP> 4.6K May 14 17:16 ./direct-minio-test.py
-rw-r--r-- 1 <USER> <GROUP> 245K May 14 17:16 ./package-lock.json
-rw-r--r-- 1 <USER> <GROUP> 4.1K May 15 15:33 ./health_checks_proposal.md
-rwxr-xr-x 1 <USER> <GROUP> 3.5K May 14 17:16 ./setup-and-run-api-tests.sh
-rwxr-xr-x 1 <USER> <GROUP> 630 May 14 17:16 ./docker-dashboard
-rwxr-xr-x 1 <USER> <GROUP> 3.3K May 14 17:16 ./verify_consolidated_endpoints.py
-rw-r--r-- 1 <USER> <GROUP> 3.3K May 14 17:16 ./refactor_friday.md
-rw-r--r-- 1 <USER> <GROUP> 604 May 14 17:16 ./Makefile
-rw-r--r-- 1 <USER> <GROUP> 1.4K May 14 17:16 ./Dockerfile.api
-rwxr-xr-x 1 <USER> <GROUP> 4.8K May 14 17:16 ./run-all-tests.sh
-rwxr-xr-x 1 <USER> <GROUP> 2.4K May 14 17:16 ./enable_test_mode_in_docker.py
-rwxr-xr-x 1 <USER> <GROUP> 4.0K May 14 17:16 ./run-tests-daemon.sh
-rw-r--r-- 1 <USER> <GROUP> 16K May 14 17:16 ./docker-dashboard.js
-rwxr-xr-x 1 <USER> <GROUP> 3.3K May 14 17:16 ./test_vm_injection.py
-rwxr-xr-x 1 <USER> <GROUP> 5.0K May 14 17:16 ./test-vm-injection-api-new.sh
-rwxr-xr-x 1 <USER> <GROUP> 2.4K May 14 17:16 ./run_tests_in_docker.sh
-rw-r--r-- 1 <USER> <GROUP> 18 May 14 17:16 ./test.txt
-rwxr-xr-x 1 <USER> <GROUP> 1.7K May 14 17:16 ./setup_test_tools.sh
-rw-r--r-- 1 <USER> <GROUP> 2.1K May 14 17:16 ./comprehensive-test.js
-rwxr-xr-x 1 <USER> <GROUP> 887 May 14 17:16 ./turdparty-dashboard.sh
-rw-r--r-- 1 <USER> <GROUP> 3.9K May 14 17:16 ./test-vm-injection-playwright.js
-rw-r--r-- 1 <USER> <GROUP> 1.3K May 14 17:16 ./pyproject.toml
-rw-r--r-- 1 <USER> <GROUP> 1.7K May 14 17:16 ./textual_ui.css
-rw-r--r-- 1 <USER> <GROUP> 5.4K May 14 17:16 ./ui_test_screenshot.png
-rw-r--r-- 1 <USER> <GROUP> 431 May 14 17:16 ./test_upload.py
-rw-r--r-- 1 <USER> <GROUP> 1.5K May 14 17:16 ./debug_file_validation.py
-rwxr-xr-x 1 <USER> <GROUP> 233 May 14 17:16 ./build-script.sh
-rwxr-xr-x 1 <USER> <GROUP> 12K May 14 17:16 ./service_monitor.py
-rwxr-xr-x 1 <USER> <GROUP> 5.4K May 14 17:16 ./run_tests.sh
-rw-r--r-- 1 <USER> <GROUP> 1.1K May 14 17:16 ./simple-test.spec.js
-rwxr-xr-x 1 <USER> <GROUP> 4.6K May 14 17:16 ./test_setup.sh
-rwxr-xr-x 1 <USER> <GROUP> 1.6K May 14 17:16 ./run_integration_tests.sh
-rwxr-xr-x 1 <USER> <GROUP> 4.2K May 14 17:16 ./test-minio-api-upload.sh
-rwxr-xr-x 1 <USER> <GROUP> 12K Jun  3 23:38 ./cleanup-master-script.sh
-rw-r--r-- 1 <USER> <GROUP> 1.8K May 14 17:16 ./status_chart.txt
-rw-r--r-- 1 <USER> <GROUP> 2.9K May 14 17:16 ./INTEGRATION-TEST-SUMMARY.md
-rw-r--r-- 1 <USER> <GROUP> 3.3K May 14 17:16 ./upload-implementation-todos.md
-rw-r--r-- 1 <USER> <GROUP> 1005 May 14 17:16 ./check_connection.py
-rw-r--r-- 1 <USER> <GROUP> 2.3K May 14 17:16 ./upload-test.js
-rw-r--r-- 1 <USER> <GROUP> 6.5K May 14 17:16 ./testing_suite_PRD.md
-rwxr-xr-x 1 <USER> <GROUP> 6.5K May 14 17:16 ./test-vm-injection-ssh-api.sh
-rw-r--r-- 1 <USER> <GROUP> 37 May 14 17:16 ./requirements-ssh.txt
-rw-r--r-- 1 <USER> <GROUP> 4.2K May 14 17:16 ./TEST_TROUBLESHOOTING.md
-rw-r--r-- 1 <USER> <GROUP> 11K May 14 17:16 ./CHANGELOG.md
-rwxr-xr-x 1 <USER> <GROUP> 8.3K May 14 17:16 ./run-non-playwright-tests.sh
-rwxr-xr-x 1 <USER> <GROUP> 5.5K May 14 17:16 ./vagrant_exec.sh
-rw-r--r-- 1 <USER> <GROUP> 2.1K May 14 17:16 ./form-test.js
-rwxr-xr-x 1 <USER> <GROUP> 3.4K May 14 17:16 ./run-test-advanced.sh
-rwxr-xr-x 1 <USER> <GROUP> 15K May 14 17:16 ./vm_template_test.py
-rw-r--r-- 1 <USER> <GROUP> 140K May 14 17:16 ./error_page.html
-rwxr-xr-x 1 <USER> <GROUP> 15K May 14 17:16 ./run-python-tests-with-docker.sh
-rwxr-xr-x 1 <USER> <GROUP> 893 May 14 17:16 ./run_vagrant_tests.sh
-rwxr-xr-x 1 <USER> <GROUP> 2.2K May 14 17:16 ./test-vagrant-ssh.sh
-rwxr-xr-x 1 <USER> <GROUP> 149 May 14 17:16 ./run_vm_test.sh
-rw-r--r-- 1 <USER> <GROUP> 4.6K May 14 17:16 ./minio-direct-test.py
-rwxr-xr-x 1 <USER> <GROUP> 2.2K May 14 17:16 ./override_dependencies.py
-rw-r--r-- 1 <USER> <GROUP> 488 May 15 10:39 ./endpoint_monitoring.log
-rw-r--r-- 1 <USER> <GROUP> 12K Jun  3 23:32 ./cleanup-phase-checklists.md
-rwxr-xr-x 1 <USER> <GROUP> 8.4K May 14 17:16 ./test_api_coverage.py
-rw-r--r-- 1 <USER> <GROUP> 4.0K May 14 17:16 ./docker_session.py
-rw-r--r-- 1 <USER> <GROUP> 4.7K Jun  3 22:18 ./cachet-implementation-complete.md
-rwxr-xr-x 1 <USER> <GROUP> 4.8K May 14 17:16 ./modify_auth_middleware.py
-rw-r--r-- 1 <USER> <GROUP> 11K May 14 17:16 ./docker-lock.json
-rwxr-xr-x 1 <USER> <GROUP> 1.9K May 14 17:16 ./test-api-urls.sh
-rw-r--r-- 1 <USER> <GROUP> 364 May 14 17:16 ./test_redirect_dockerfile
-rw-r--r-- 1 <USER> <GROUP> 52K May 14 14:11 ./.coverage
-rw-r--r-- 1 <USER> <GROUP> 2.2K May 14 17:16 ./implementation_vs_pending.txt
-rw-r--r-- 1 <USER> <GROUP> 355 May 15 19:09 ./mail_response.html
-rwxr-xr-x 1 <USER> <GROUP> 12K May 14 17:16 ./test-appimage-api-workflow.sh
-rwxr-xr-x 1 <USER> <GROUP> 3.9K May 14 17:16 ./check_test_env.py
-rw-r--r-- 1 <USER> <GROUP> 7.3K May 14 17:16 ./selenium_capture_screenshots.py
-rw-r--r-- 1 <USER> <GROUP> 49M May 15 14:37 ./ai_references_report.md
-rwxr-xr-x 1 <USER> <GROUP> 7.6K May 14 17:16 ./run-all-tests-new.sh
-rw-r--r-- 1 <USER> <GROUP> 28K Jun  3 23:24 ./test_db.sqlite
-rw-r--r-- 1 <USER> <GROUP> 5.5K May 14 17:16 ./homepage.png
