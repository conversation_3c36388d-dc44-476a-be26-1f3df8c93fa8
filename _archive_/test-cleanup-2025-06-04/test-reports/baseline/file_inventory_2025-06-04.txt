# TurdParty Test Infrastructure File Inventory
# Generated: Wed  4 Jun 12:11:20 CEST 2025
# Purpose: Document all test-related files before cleanup

## Directory: test_logs
-rw-r--r-- 1 <USER> <GROUP> 64136 Apr  8 14:07 test_logs/non_playwright_test_run_20250408_140728.log
-rw-r--r-- 1 <USER> <GROUP> 65714 Apr  8 14:55 test_logs/python_tests_volume_20250408_145509.log
-rw-r--r-- 1 <USER> <GROUP> 1649 May  9 12:41 test_logs/test_summary.txt
-rw-r--r-- 1 <USER> <GROUP> 553 Apr  8 14:55 test_logs/current_test.log
-rw-r--r-- 1 <USER> <GROUP> 271043 Apr  8 14:18 test_logs/python_tests_volume_20250408_140841.log
-rw-r--r-- 1 <USER> <GROUP> 319628 Apr  8 14:08 test_logs/python_tests_volume_20250408_140745.log
-rw-r--r-- 1 <USER> <GROUP> 46595 Apr 17 13:22 test_logs/python_test_run_20250417_132259.log
-rw-r--r-- 1 <USER> <GROUP> 64136 Apr  8 14:53 test_logs/non_playwright_test_run_20250408_145327.log
-rw-r--r-- 1 <USER> <GROUP> 89279 Apr  8 14:44 test_logs/python_tests_volume_20250408_144338.log
-rw-r--r-- 1 <USER> <GROUP> 266 Apr  8 13:38 test_logs/specific_test_summary.txt
-rw-r--r-- 1 <USER> <GROUP> 725 Apr  8 14:00 test_logs/comprehensive_test_run_20250408_140015.log
-rw-r--r-- 1 <USER> <GROUP> 111 Apr  8 14:18 test_logs/streamlit_test_failures.txt
Total files: 12

## Directory: test_results
Total files: 0

## Directory: test_screenshots
-rw-r--r-- 1 <USER> <GROUP> 63374 Apr 17 10:12 test_screenshots/error_page_navigation.png
-rw-r--r-- 1 <USER> <GROUP> 63223 Apr 17 10:12 test_screenshots/url_param_3_specific_id.png
-rw-r--r-- 1 <USER> <GROUP> 34606 Apr 17 10:12 test_screenshots/file_workflow_1_start.png
-rw-r--r-- 1 <USER> <GROUP> 10447 Apr 17 10:12 test_screenshots/url_param_1_default.png
-rw-r--r-- 1 <USER> <GROUP> 63209 Apr 17 10:12 test_screenshots/deep_link_vm_details.png
-rw-r--r-- 1 <USER> <GROUP> 10459 Apr 17 10:12 test_screenshots/api_rate_limit.png
-rw-r--r-- 1 <USER> <GROUP> 50379 Apr 17 10:12 test_screenshots/state_1_filter_applied.png
-rwxrwxrwx 1 <USER> <GROUP> 4073 Apr  8 13:41 test_screenshots/verification-basic-basic-test-chromium-retry1/trace.zip
-rw-r--r-- 1 <USER> <GROUP> 34514 Apr 17 10:12 test_screenshots/file_workflow_3_uploaded.png
-rw-r--r-- 1 <USER> <GROUP> 34536 Apr 17 10:12 test_screenshots/file_upload_form_initial.png
-rwxrwxrwx 1 <USER> <GROUP> 4153 Apr  8 13:41 test_screenshots/verification-upload-file-upload-test-chromium-retry1/trace.zip
-rw-r--r-- 1 <USER> <GROUP> 37949 Apr 17 10:12 test_screenshots/vm_status_initial.png
-rw-r--r-- 1 <USER> <GROUP> 63466 Apr 17 10:12 test_screenshots/workflow_1_home.png
-rw-r--r-- 1 <USER> <GROUP> 10517 Apr 17 10:12 test_screenshots/state_2_return_to_page.png
-rw-r--r-- 1 <USER> <GROUP> 47374 Apr 17 10:12 test_screenshots/vm_form_empty_submit.png
-rw-r--r-- 1 <USER> <GROUP> 63252 Apr 17 10:12 test_screenshots/vm_details_not_found.png
-rw-r--r-- 1 <USER> <GROUP> 36936 Apr 17 10:12 test_screenshots/url_param_2_filtered.png
-rw-r--r-- 1 <USER> <GROUP> 29045 Apr 17 10:12 test_screenshots/vm_form_initial.png
-rw-r--r-- 1 <USER> <GROUP> 34606 Apr 17 10:12 test_screenshots/file_workflow_2_selected.png
-rwxr-xr-x 1 <USER> <GROUP> 145 May  9 12:41 test_screenshots/.last-run.json
Total files: 20

## Directory: test_upload_dir
-rw-r--r-- 1 <USER> <GROUP> 15 Mar 21 09:45 test_upload_dir/file2.txt
-rw-r--r-- 1 <USER> <GROUP> 22 Mar 21 09:45 test_upload_dir/subdir/file3.txt
-rw-r--r-- 1 <USER> <GROUP> 15 Mar 21 09:45 test_upload_dir/file1.txt
Total files: 3

## Directory: test_vm
-rwxr-xr-x 1 <USER> <GROUP> 3508 May  9 12:41 test_vm/test_injection.py
-rwxr-xr-x 1 <USER> <GROUP> 1624 May  9 12:41 test_vm/inject_file.sh
-rw-r--r-- 1 <USER> <GROUP> 221 Apr  8 13:30 test_vm/shell.nix
-rw-r--r-- 1 <USER> <GROUP> 2000 May  9 12:41 test_vm/README.md
-rw-r--r-- 1 <USER> <GROUP> 423 Apr  8 13:30 test_vm/.vagrant/rgloader/loader.rb
-rw-r--r-- 1 <USER> <GROUP> 32 Apr  8 13:30 test_vm/.vagrant/machines/default/libvirt/index_uuid
-rw-r--r-- 1 <USER> <GROUP> 40 Apr  8 13:30 test_vm/.vagrant/machines/default/libvirt/action_provision
-rw-r--r-- 1 <USER> <GROUP> 111 May  9 12:41 test_vm/.vagrant/machines/default/libvirt/created_networks
-rw-r--r-- 1 <USER> <GROUP> 38 Apr  8 13:30 test_vm/.vagrant/machines/default/libvirt/vagrant_cwd
-rw------- 1 <USER> <GROUP> 400 Apr  8 13:30 test_vm/.vagrant/machines/default/libvirt/private_key
-rw-r--r-- 1 <USER> <GROUP> 36 Apr  8 13:30 test_vm/.vagrant/machines/default/libvirt/id
-rw-r--r-- 1 <USER> <GROUP> 142 Apr 22 13:34 test_vm/.vagrant/machines/default/libvirt/box_meta
-rw-r--r-- 1 <USER> <GROUP> 4 Apr  8 13:30 test_vm/.vagrant/machines/default/libvirt/creator_uid
-rw-r--r-- 1 <USER> <GROUP> 2 Apr 22 13:34 test_vm/.vagrant/machines/default/libvirt/synced_folders
-rw-r--r-- 1 <USER> <GROUP> 176 May  9 12:41 test_vm/test_file.txt
-rw-r--r-- 1 <USER> <GROUP> 1814 May  9 12:41 test_vm/Vagrantfile
-rwxr-xr-x 1 <USER> <GROUP> 2016 May  9 12:41 test_vm/inject_file_cat.sh
-rwxr-xr-x 1 <USER> <GROUP> 629 May  9 12:41 test_vm/stop_vm.sh
-rwxr-xr-x 1 <USER> <GROUP> 3372 May  9 12:41 test_vm/test_injection_vagrant.py
-rwxr-xr-x 1 <USER> <GROUP> 1366 May  9 12:41 test_vm/start_vm.sh
Total files: 20

## Directory: test-dir
-rw-r--r-- 1 <USER> <GROUP> 171 Apr  8 13:30 test-dir/test.spec.js
-rw-r--r-- 1 <USER> <GROUP> 280 Apr  8 13:30 test-dir/package.json
-rw-r--r-- 1 <USER> <GROUP> 1546 Apr  8 13:30 test-dir/node_modules/.package-lock.json
-rw-r--r-- 1 <USER> <GROUP> 254 Apr  8 13:30 test-dir/node_modules/@playwright/test/NOTICE
-rw-r--r-- 1 <USER> <GROUP> 11601 Apr  8 13:30 test-dir/node_modules/@playwright/test/LICENSE
-rw-r--r-- 1 <USER> <GROUP> 645 Apr  8 13:30 test-dir/node_modules/@playwright/test/reporter.mjs
-rw-r--r-- 1 <USER> <GROUP> 8120 Apr  8 13:30 test-dir/node_modules/@playwright/test/README.md
-rw-r--r-- 1 <USER> <GROUP> 650 Apr  8 13:30 test-dir/node_modules/@playwright/test/reporter.d.ts
-rwxr-xr-x 1 <USER> <GROUP> 707 Apr  8 13:30 test-dir/node_modules/@playwright/test/cli.js
-rw-r--r-- 1 <USER> <GROUP> 679 Apr  8 13:30 test-dir/node_modules/@playwright/test/index.d.ts
-rw-r--r-- 1 <USER> <GROUP> 754 Apr  8 13:30 test-dir/node_modules/@playwright/test/package.json
-rw-r--r-- 1 <USER> <GROUP> 679 Apr  8 13:30 test-dir/node_modules/@playwright/test/index.mjs
-rw-r--r-- 1 <USER> <GROUP> 648 Apr  8 13:30 test-dir/node_modules/@playwright/test/index.js
-rw-r--r-- 1 <USER> <GROUP> 645 Apr  8 13:30 test-dir/node_modules/@playwright/test/reporter.js
-rw-r--r-- 1 <USER> <GROUP> 254 Apr  8 13:30 test-dir/node_modules/playwright/NOTICE
-rw-r--r-- 1 <USER> <GROUP> 11601 Apr  8 13:30 test-dir/node_modules/playwright/LICENSE
-rw-r--r-- 1 <USER> <GROUP> 806 Apr  8 13:30 test-dir/node_modules/playwright/test.js
-rw-r--r-- 1 <USER> <GROUP> 348866 Apr  8 13:30 test-dir/node_modules/playwright/types/test.d.ts
-rw-r--r-- 1 <USER> <GROUP> 26840 Apr  8 13:30 test-dir/node_modules/playwright/types/testReporter.d.ts
-rw-r--r-- 1 <USER> <GROUP> 8120 Apr  8 13:30 test-dir/node_modules/playwright/README.md
Total files: 61

## Directory: test-files
-rw-r--r-- 1 <USER> <GROUP> 48 Apr  8 13:30 test-files/test-folder/nested-file.txt
-rw-r--r-- 1 <USER> <GROUP> 39 May  9 12:41 test-files/test-upload.txt
Total files: 2

## Directory: test-reports
-rw-r--r-- 1 <USER> <GROUP> 493307 Apr  8 13:30 test-reports/index.html
-rw-r--r-- 1 <USER> <GROUP> 19829 May  9 12:41 test-reports/analysis/coverage_analysis.txt
-rw-r--r-- 1 <USER> <GROUP> 16508 Jun  3 23:45 test-reports/baseline/inventory/root_files.txt
-rw-r--r-- 1 <USER> <GROUP> 4395 Jun  3 23:45 test-reports/baseline/inventory/python_files_sample.txt
-rw-r--r-- 1 <USER> <GROUP> 30950 Jun  3 23:45 test-reports/baseline/inventory/test_files.txt
-rw-r--r-- 1 <USER> <GROUP> 94 Jun  3 23:45 test-reports/baseline/logs/pytest_output.txt
-rw-r--r-- 1 <USER> <GROUP> 41461 Jun  3 23:44 test-reports/baseline/baseline_generation_20250603_234407.log
-rw-r--r-- 1 <USER> <GROUP> 41461 Jun  3 23:45 test-reports/baseline/baseline_generation_20250603_234530.log
-rw-r--r-- 1 <USER> <GROUP> 35 Jun  3 23:45 test-reports/baseline/performance/execution_times.csv
-rw-r--r-- 1 <USER> <GROUP> 6847 Jun  4 12:11 test-reports/baseline/file_inventory_2025-06-04.txt
-rw-r--r-- 1 <USER> <GROUP> 40926 May  8 12:42 test-reports/coverage/html/z_b390d5b1cbeeba9b_user_py.html
-rw-r--r-- 1 <USER> <GROUP> 5088 May  8 12:42 test-reports/coverage/html/z_10fae538ba4e8521___init___py.html
-rw-r--r-- 1 <USER> <GROUP> 26422 May  8 12:42 test-reports/coverage/html/z_eb3a433c7a885d4e_vm_injection_py.html
-rw-r--r-- 1 <USER> <GROUP> 5557 May  8 12:42 test-reports/coverage/html/z_0f3712b6a4ece250___init___py.html
-rw-r--r-- 1 <USER> <GROUP> 52998 May  8 12:42 test-reports/coverage/html/z_eb24b92fea283298_base_model_py.html
-rw-r--r-- 1 <USER> <GROUP> 44401 May  8 14:24 test-reports/coverage/html/index.html
-rw-r--r-- 1 <USER> <GROUP> 7085 May  8 12:42 test-reports/coverage/html/z_10fae538ba4e8521_dependencies_py.html
-rw-r--r-- 1 <USER> <GROUP> 4904 May  8 12:42 test-reports/coverage/html/z_1994b0afda3e05cc___init___py.html
-rw-r--r-- 1 <USER> <GROUP> 172718 May  8 14:24 test-reports/coverage/html/class_index.html
-rw-r--r-- 1 <USER> <GROUP> 116634 May  8 12:42 test-reports/coverage/html/z_4fb5f34370673e01_docs_py.html
Total files: 148

## Directory: test-results
-rw-r--r-- 1 <USER> <GROUP> 45 May  9 12:41 test-results/.last-run.json
Total files: 1

## Directory: test-vm
-rw-r--r-- 1 <USER> <GROUP> 423 May  9 12:41 test-vm/.vagrant/rgloader/loader.rb
-rw-r--r-- 1 <USER> <GROUP> 32 May  9 12:41 test-vm/.vagrant/machines/default/virtualbox/index_uuid
-rw-r--r-- 1 <USER> <GROUP> 36 May  9 12:41 test-vm/.vagrant/machines/default/virtualbox/id
-rw-r--r-- 1 <USER> <GROUP> 10 May  9 12:41 test-vm/.vagrant/machines/default/virtualbox/action_set_name
-rw-r--r-- 1 <USER> <GROUP> 4 May  9 12:41 test-vm/.vagrant/machines/default/virtualbox/creator_uid
-rw-r--r-- 1 <USER> <GROUP> 3388 May  9 12:41 test-vm/Vagrantfile
Total files: 6

## Directory: tests/
-rw-r--r-- 1 <USER> <GROUP> 302 May 14 09:12 tests/.pytest_cache/README.md
-rw-r--r-- 1 <USER> <GROUP> 37 May 14 09:12 tests/.pytest_cache/.gitignore
-rw-r--r-- 1 <USER> <GROUP> 2 May 14 15:50 tests/.pytest_cache/v/cache/stepwise
-rw-r--r-- 1 <USER> <GROUP> 7728 May 14 15:50 tests/.pytest_cache/v/cache/nodeids
-rw-r--r-- 1 <USER> <GROUP> 2207 May 14 15:38 tests/.pytest_cache/v/cache/lastfailed
-rw-r--r-- 1 <USER> <GROUP> 191 May 14 09:12 tests/.pytest_cache/CACHEDIR.TAG
-rwxr-xr-x 1 <USER> <GROUP> 4726 May 14 17:16 tests/minio/test-host-minio.sh
-rwxr-xr-x 1 <USER> <GROUP> 3538 May 14 17:16 tests/minio/test-minio-upload.sh
-rw-r--r-- 1 <USER> <GROUP> 4648 May 14 17:16 tests/minio/direct-minio-test.py
-rw-r--r-- 1 <USER> <GROUP> 4648 May 14 17:16 tests/minio/minio-direct-test.py
-rw-r--r-- 1 <USER> <GROUP> 619 May 14 17:16 tests/fixtures/ui_test.js
-rw-r--r-- 1 <USER> <GROUP> 1762 May 14 17:16 tests/fixtures/final-test.js
-rw-r--r-- 1 <USER> <GROUP> 884 May 14 17:16 tests/fixtures/ui_components_test.js
-rw-r--r-- 1 <USER> <GROUP> 936 May 14 17:16 tests/fixtures/basic-test.js
-rw-r--r-- 1 <USER> <GROUP> 2123 May 14 17:16 tests/fixtures/comprehensive-test.js
-rw-r--r-- 1 <USER> <GROUP> 2117 May 14 17:16 tests/fixtures/form-test.js
-rw-r--r-- 1 <USER> <GROUP> 1991 Mar 21 07:57 tests/minimal-upload.spec.js
-rwxr-xr-x 1 <USER> <GROUP> 3992 Mar 20 21:39 tests/template-transfer.spec.js
-rwxr-xr-x 1 <USER> <GROUP> 1783 May 14 19:24 tests/scripts/run-celery-tests.sh
-rwxr-xr-x 1 <USER> <GROUP> 14475 May 14 17:16 tests/scripts/run-comprehensive-tests.sh
Total files: 209

