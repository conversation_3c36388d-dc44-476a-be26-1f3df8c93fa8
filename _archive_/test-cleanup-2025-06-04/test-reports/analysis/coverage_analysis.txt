================================================================================
TurdParty Test Coverage Analysis
Generated: 2025-05-08 14:24:26
================================================================================

OVERALL COVERAGE
--------------------------------------------------------------------------------
Total Lines: 8233
Covered Lines: 2596
Missing Lines: 5637
Coverage: 31.5%

COVERAGE BY MODULE
--------------------------------------------------------------------------------
Module                                             Coverage   Lines      Missing   
api.__init__                                       100.0      0          0         
api.db.base                                        100.0      6          0         
api.db.models.__init__                             100.0      9          0         
api.db.models.audit                                100.0      12         0         
api.db.models.file_selection                       100.0      13         0         
api.db.models.file_upload                          100.0      22         0         
api.db.models.hash_report                          100.0      12         0         
api.db.models.session                              100.0      14         0         
api.db.types                                       100.0      15         0         
api.models.__init__                                100.0      4          0         
api.models.file_selection                          100.0      33         0         
api.models.item                                    100.0      11         0         
api.models.user                                    100.0      23         0         
api.models.vagrant_vm                              100.0      77         0         
api.models.vm_injection                            100.0      54         0         
api.routes.__init__                                100.0      1          0         
api.routes.test.__init__                           100.0      2          0         
api.schemas.__init__                               100.0      6          0         
api.schemas.auth                                   100.0      17         0         
api.schemas.base                                   100.0      22         0         
api.schemas.mfa                                    100.0      17         0         
api.schemas.static_analysis                        100.0      33         0         
api.schemas.token                                  100.0      24         0         
api.schemas.user                                   100.0      33         0         
api.schemas.virustotal                             100.0      9          0         
api.services.__init__                              100.0      2          0         
api.services.metrics.__init__                      100.0      0          0         
api.static.__init__                                100.0      0          0         
api.core.config                                    91.6       83         7         
api.core.logging_config                            90.0       50         5         
api.models.base                                    87.0       23         3         
api.models.success_criteria                        87.0       46         6         
api.routes.test.live_edit_test                     80.0       5          1         
api.schemas.vagrant_vm                             76.7       73         17        
api.routes.items                                   75.0       16         4         
api.routes.users                                   74.2       31         8         
api.schemas.file_upload                            73.6       53         14        
api.routes.logs                                    73.2       41         11        
api.schemas.vm_injection                           72.5       51         14        
api.schemas.item                                   68.4       38         12        
api.db.models.vm_injection                         67.6       37         12        
api.db.models.user                                 63.9       61         22        
api.core.endpoints                                 63.6       22         8         
api.core.exceptions                                63.4       71         26        
api.routes.static_files                            61.5       13         5         
api.core.docker_utils                              60.0       60         24        
api.schemas.file_selection                         60.0       50         20        
api.db.models.item                                 58.5       41         17        
api.core.errors                                    56.1       148        65        
api.routes.vm_injection                            55.8       43         19        
api.routes.auth                                    54.8       124        56        
api.routes.minio_ssh_wrapper                       49.3       69         35        
api.routes.swagger_ui                              48.0       25         13        
api.db.base_model                                  46.5       86         46        
api.core.auth                                      45.2       31         17        
api.middleware.endpoint_monitoring_middleware      45.2       31         17        
api.services.auth                                  44.4       54         30        
api.routes.consolidated_vagrant                    40.9       254        150       
api.middleware.api_version_middleware              40.9       22         13        
api.routes.minio_status                            40.4       52         31        
api.middleware.security_headers                    40.0       15         9         
api.routes.docker                                  39.0       41         25        
api.db.session                                     38.3       60         37        
api.routes.vagrant_vm                              37.2       78         49        
api.db.models.vagrant_vm                           37.1       143        90        
api.routes.file_to_vm                              36.8       57         36        
api.services.file_validation                       36.8       57         36        
api.routes.vagrant                                 36.0       214        137       
api.core.db                                        35.8       179        115       
api.routes.success_criteria                        35.7       56         36        
api.routes.mfa                                     35.3       51         33        
api.routes.virustotal                              35.3       34         22        
api.routes.file_selection                          35.0       60         39        
api.routes.system                                  34.0       47         31        
api.core.security                                  33.3       51         34        
api.db.repositories.user                           32.6       92         62        
api.routes.file_upload                             32.3       96         65        
api.db.dependencies                                30.6       36         25        
api.routes.static_analysis                         30.0       60         42        
api.middleware.auth_middleware                     29.1       86         61        
api.db.repositories.base                           28.6       56         40        
api.services.base                                  28.0       75         54        
api.services.file_upload_service                   27.2       125        91        
api.services.file_to_vm_bridge                     27.0       63         46        
api.middleware.mfa_middleware                      26.8       41         30        
api.routes.storage                                 25.9       147        109       
api.services.virustotal_service                    23.5       51         39        
api.services.success_criteria                      22.4       58         45        
api.core.dependencies                              22.2       54         42        
api.routes.minio_health                            22.2       63         49        
api.services.minio_ssh_client                      20.8       264        209       
api.services.mfa_service                           20.4       98         78        
api.routes.docs                                    19.6       92         74        
api.routes.endpoint_monitoring                     19.0       84         68        
api.services.static_analysis_service               18.3       104        85        
api.services.vagrant_vm                            17.0       206        171       
api.core.logging                                   16.7       72         60        
api.services.docker_service                        15.7       83         70        
api.application                                    13.8       269        232       
api.routes.health                                  12.7       221        193       
api.services.fathom_ssh_client                     11.6       277        245       
api.services.vagrant_client                        9.3        300        272       
api.services.vm_injection                          8.3        230        211       
api.api                                            0.0        9          9         
api.dependencies                                   0.0        4          4         
api.models.file_upload                             0.0        37         37        
api.routes.api                                     0.0        12         12        
api.routes.fathom                                  0.0        154        154       
api.routes.file_upload_new                         0.0        101        101       
api.routes.local_swagger                           0.0        13         13        
api.routes.minimal_swagger                         0.0        13         13        
api.routes.standalone_swagger                      0.0        21         21        
api.routes.swagger_v2                              0.0        13         13        
api.routes.test                                    0.0        5          5         
api.routes.vm                                      0.0        55         55        
api.services.coverage_service                      0.0        73         73        
api.services.file_selection                        0.0        68         68        
api.services.file_upload                           0.0        173        173       
api.services.file_validation_new                   0.0        64         64        
api.services.item                                  0.0        125        125       
api.services.metrics.prometheus                    0.0        46         46        
api.services.notification_service                  0.0        99         99        
api.services.scheduler_service                     0.0        131        131       
api.services.static_analysis                       0.0        7          7         
api.services.user                                  0.0        68         68        
api.services.vagrant_service                       0.0        115        115       
api.static.swagger_ui_assets                       0.0        6          6         

LOW COVERAGE MODULES (Below Critical Threshold)
--------------------------------------------------------------------------------
Module                                             Coverage   Lines      Missing   
api.api                                            0.0        9          9         
api.dependencies                                   0.0        4          4         
api.models.file_upload                             0.0        37         37        
api.routes.api                                     0.0        12         12        
api.routes.fathom                                  0.0        154        154       
api.routes.file_upload_new                         0.0        101        101       
api.routes.local_swagger                           0.0        13         13        
api.routes.minimal_swagger                         0.0        13         13        
api.routes.standalone_swagger                      0.0        21         21        
api.routes.swagger_v2                              0.0        13         13        
api.routes.test                                    0.0        5          5         
api.routes.vm                                      0.0        55         55        
api.services.coverage_service                      0.0        73         73        
api.services.file_selection                        0.0        68         68        
api.services.file_upload                           0.0        173        173       
api.services.file_validation_new                   0.0        64         64        
api.services.item                                  0.0        125        125       
api.services.metrics.prometheus                    0.0        46         46        
api.services.notification_service                  0.0        99         99        
api.services.scheduler_service                     0.0        131        131       
api.services.static_analysis                       0.0        7          7         
api.services.user                                  0.0        68         68        
api.services.vagrant_service                       0.0        115        115       
api.static.swagger_ui_assets                       0.0        6          6         
api.services.vm_injection                          8.3        230        211       
api.services.vagrant_client                        9.3        300        272       
api.services.fathom_ssh_client                     11.6       277        245       
api.routes.health                                  12.7       221        193       
api.application                                    13.8       269        232       
api.services.docker_service                        15.7       83         70        
api.core.logging                                   16.7       72         60        
api.services.vagrant_vm                            17.0       206        171       
api.services.static_analysis_service               18.3       104        85        
api.routes.endpoint_monitoring                     19.0       84         68        
api.routes.docs                                    19.6       92         74        
api.services.mfa_service                           20.4       98         78        
api.services.minio_ssh_client                      20.8       264        209       
api.core.dependencies                              22.2       54         42        
api.routes.minio_health                            22.2       63         49        
api.services.success_criteria                      22.4       58         45        
api.services.virustotal_service                    23.5       51         39        
api.routes.storage                                 25.9       147        109       
api.middleware.mfa_middleware                      26.8       41         30        
api.services.file_to_vm_bridge                     27.0       63         46        
api.services.file_upload_service                   27.2       125        91        
api.services.base                                  28.0       75         54        
api.db.repositories.base                           28.6       56         40        
api.middleware.auth_middleware                     29.1       86         61        
api.routes.static_analysis                         30.0       60         42        
api.db.dependencies                                30.6       36         25        
api.routes.file_upload                             32.3       96         65        
api.db.repositories.user                           32.6       92         62        
api.core.security                                  33.3       51         34        
api.routes.system                                  34.0       47         31        
api.routes.file_selection                          35.0       60         39        
api.routes.mfa                                     35.3       51         33        
api.routes.virustotal                              35.3       34         22        
api.routes.success_criteria                        35.7       56         36        
api.core.db                                        35.8       179        115       
api.routes.vagrant                                 36.0       214        137       
api.routes.file_to_vm                              36.8       57         36        
api.services.file_validation                       36.8       57         36        
api.db.models.vagrant_vm                           37.1       143        90        
api.routes.vagrant_vm                              37.2       78         49        
api.db.session                                     38.3       60         37        
api.routes.docker                                  39.0       41         25        
api.middleware.security_headers                    40.0       15         9         
api.routes.minio_status                            40.4       52         31        
api.middleware.api_version_middleware              40.9       22         13        
api.routes.consolidated_vagrant                    40.9       254        150       
api.services.auth                                  44.4       54         30        
api.core.auth                                      45.2       31         17        
api.middleware.endpoint_monitoring_middleware      45.2       31         17        
api.db.base_model                                  46.5       86         46        
api.routes.swagger_ui                              48.0       25         13        
api.routes.minio_ssh_wrapper                       49.3       69         35        
api.routes.auth                                    54.8       124        56        
api.routes.vm_injection                            55.8       43         19        
api.core.errors                                    56.1       148        65        
api.db.models.item                                 58.5       41         17        

UNCOVERED LINES IN CRITICAL MODULES
--------------------------------------------------------------------------------
Module: api.routes.file_upload (32.3% covered)
Missing lines: 72, 73, 74, 75, 76, 77, 78, 115, 116, 119, 122, 129, 130, 131, 132, 133, 134, 135, 164, 165, 166, 167, 168, 173, 175, 180, 181, 182, 183, 184, 185, 212, 213, 214, 215, 216, 220, 221, 222, 223, 224, 225, 254, 255, 256, 257, 258, 262, 263, 264, 265, 266, 267, 302, 303, 304, 305, 310, 311, 316, 317, 318, 319, 320, 337

Module: api.routes.minio_ssh_wrapper (49.3% covered)
Missing lines: 59, 60, 61, 68, 69, 72, 73, 74, 76, 84, 87, 90, 91, 92, 95, 97, 98, 100, 101, 102, 104, 105, 106, 109, 110, 119, 120, 132, 133, 142, 143, 152, 153, 177, 178

Module: api.routes.vagrant_vm (37.2% covered)
Missing lines: 32, 33, 34, 45, 46, 47, 48, 52, 66, 69, 70, 71, 72, 77, 89, 90, 103, 104, 125, 128, 129, 130, 136, 139, 140, 141, 143, 144, 145, 146, 148, 149, 150, 151, 153, 155, 161, 172, 173, 185, 186, 187, 198, 199, 200, 205, 207, 213, 223

Module: api.schemas.file_upload (73.6% covered)
Missing lines: 64, 65, 68, 69, 70, 72, 184, 185, 188, 189, 190, 191, 192, 194

Module: api.services.file_upload_service (27.2% covered)
Missing lines: 49, 50, 52, 53, 54, 55, 56, 57, 58, 60, 61, 62, 63, 64, 65, 66, 67, 70, 71, 89, 91, 94, 95, 97, 99, 100, 112, 113, 114, 116, 129, 131, 134, 137, 140, 141, 143, 154, 157, 158, 159, 168, 169, 181, 182, 183, 197, 199, 202, 203, 204, 207, 210, 211, 212, 221, 230, 237, 238, 239, 251, 253, 254, 256, 257, 258, 259, 266, 267, 270, 271, 272, 274, 275, 276, 279, 280, 283, 284, 285, 286, 288, 289, 290, 291, 292, 293, 294, 300, 301, 302

