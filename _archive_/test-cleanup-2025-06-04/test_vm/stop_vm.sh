#!/bin/bash

# Script to stop the VM

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}Stopping VM...${NC}"

# Change to the VM directory
cd "$(dirname "$0")"

# Check if the VM is running
if vagrant status | grep -q "running"; then
    # Stop the VM
    vagrant halt
    
    # Check if the VM stopped successfully
    if [ $? -ne 0 ]; then
        echo -e "${RED}Failed to stop VM!${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}VM stopped successfully.${NC}"
else
    echo -e "${YELLOW}VM is not running.${NC}"
fi

# Exit successfully
exit 0
