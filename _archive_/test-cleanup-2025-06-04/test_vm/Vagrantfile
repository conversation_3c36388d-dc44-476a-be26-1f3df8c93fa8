# -*- mode: ruby -*-
# vi: set ft=ruby :

# All Vagrant configuration is done below. The "2" in Vagrant.configure
# configures the configuration version (we support older styles for
# backwards compatibility). Please don't change it unless you know what
# you're doing.
Vagrant.configure("2") do |config|
  # Use Ubuntu 20.04 LTS (Focal Fossa) as the base box
  config.vm.box = "generic/ubuntu2004"

  # Configure VM settings
  config.vm.provider "virtualbox" do |vb|
    # Set VM name
    vb.name = "vm_injection_test"

    # Allocate resources
    vb.memory = 1024  # 1GB RAM
    vb.cpus = 1       # 1 CPU
  end

  # Network configuration
  config.vm.network "private_network", type: "dhcp"

  # Port forwarding
  config.vm.network "forwarded_port", guest: 22, host: 2222, id: "ssh"

  # VM hostname
  config.vm.hostname = "vm-injection-test"

  # Provision the VM with necessary tools
  config.vm.provision "shell", inline: <<-SHELL
    # Update package lists
    apt-get update

    # Install necessary packages
    apt-get install -y python3 python3-pip

    # Create a test directory for file injection
    mkdir -p /tmp/injection_test
    chmod 777 /tmp/injection_test

    # Create a test user for file injection
    useradd -m -s /bin/bash testuser
    echo "testuser:password" | chpasswd

    # Add testuser to sudoers
    echo "testuser ALL=(ALL) NOPASSWD:ALL" > /etc/sudoers.d/testuser

    # Set up SSH for the test user
    mkdir -p /home/<USER>/.ssh
    chmod 700 /home/<USER>/.ssh
    touch /home/<USER>/.ssh/authorized_keys
    chmod 600 /home/<USER>/.ssh/authorized_keys
    chown -R testuser:testuser /home/<USER>/.ssh

    # Print VM information
    echo "VM is ready for testing!"
    echo "Hostname: $(hostname)"
    echo "IP Address: $(hostname -I | awk '{print $1}')"
  SHELL
end
