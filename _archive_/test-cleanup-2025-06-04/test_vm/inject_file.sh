#!/bin/bash

# Script to inject a file into the Vagrant VM

# Set up colors for output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Default values
SOURCE_FILE="test_file.txt"
TARGET_PATH="/tmp/injection_test/test_file.txt"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  case $1 in
    --source-file)
      SOURCE_FILE="$2"
      shift 2
      ;;
    --target-path)
      TARGET_PATH="$2"
      shift 2
      ;;
    *)
      echo -e "${RED}Unknown option: $1${NC}"
      exit 1
      ;;
  esac
done

# Get the absolute path to the source file
SOURCE_FILE_ABS=$(realpath "$SOURCE_FILE")

# Check if the source file exists
if [ ! -f "$SOURCE_FILE_ABS" ]; then
  echo -e "${RED}Source file does not exist: $SOURCE_FILE_ABS${NC}"
  exit 1
fi

echo -e "${YELLOW}Injecting file $SOURCE_FILE_ABS into VM at $TARGET_PATH${NC}"

# Create the target directory on the VM if it doesn't exist
TARGET_DIR=$(dirname "$TARGET_PATH")
echo -e "${YELLOW}Creating target directory: $TARGET_DIR${NC}"
vagrant ssh -c "sudo mkdir -p $TARGET_DIR"

# Copy the file to the VM
echo -e "${YELLOW}Copying file to VM...${NC}"
vagrant ssh -c "sudo cp /vagrant/$SOURCE_FILE $TARGET_PATH"

# Set permissions on the target file
echo -e "${YELLOW}Setting permissions...${NC}"
vagrant ssh -c "sudo chmod 0755 $TARGET_PATH"

# Verify the file injection
echo -e "${YELLOW}Verifying file injection...${NC}"
vagrant ssh -c "ls -la $TARGET_PATH"

# Read the file content
echo -e "${YELLOW}Reading file content...${NC}"
vagrant ssh -c "cat $TARGET_PATH"

echo -e "${GREEN}File injection completed successfully!${NC}"
