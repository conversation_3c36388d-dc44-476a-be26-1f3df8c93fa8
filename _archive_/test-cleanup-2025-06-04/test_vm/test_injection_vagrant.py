#!/usr/bin/env python3

"""
Test script for VM injection using Vagrant commands.
"""

import os
import sys
import logging
import subprocess
import argparse

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_vagrant_command(command):
    """
    Run a command on the Vagrant VM.
    
    Args:
        command: The command to run.
        
    Returns:
        The output of the command.
    """
    # Construct the Vagrant SSH command
    vagrant_command = f"vagrant ssh -c \"{command}\""
    
    # Run the command
    logger.info(f"Running command: {vagrant_command}")
    result = subprocess.run(vagrant_command, shell=True, capture_output=True, text=True)
    
    # Check if the command was successful
    if result.returncode == 0:
        logger.info(f"Command succeeded: {result.stdout}")
        return result.stdout
    else:
        logger.error(f"Command failed: {result.stderr}")
        return result.stderr

def test_injection(source_path, target_path):
    """
    Test VM injection using Vagrant commands.
    
    Args:
        source_path: The path to the source file.
        target_path: The path to the target file.
    """
    # Get the absolute path to the source file
    abs_source_path = os.path.abspath(source_path)
    
    # Check if the source file exists
    if not os.path.exists(abs_source_path):
        logger.error(f"Source file does not exist: {abs_source_path}")
        return
    
    # Create the target directory on the VM if it doesn't exist
    target_dir = os.path.dirname(target_path)
    run_vagrant_command(f"sudo mkdir -p {target_dir}")
    
    # Copy the file to the VM
    logger.info(f"Copying file {abs_source_path} to VM at {target_path}")
    
    # Use the Vagrant file provisioner to copy the file
    # First, copy the file to the /vagrant directory
    run_vagrant_command(f"sudo cp /vagrant/{os.path.basename(source_path)} {target_path}")
    
    # Set permissions on the target file
    run_vagrant_command(f"sudo chmod 0755 {target_path}")
    
    # Verify the file injection
    logger.info(f"Verifying file injection at {target_path}")
    output = run_vagrant_command(f"ls -la {target_path}")
    logger.info(f"File verification result: {output}")
    
    # Read the file content
    logger.info(f"Reading file content from {target_path}")
    output = run_vagrant_command(f"cat {target_path}")
    logger.info(f"File content: {output}")

def parse_args():
    """
    Parse command line arguments.
    
    Returns:
        The parsed arguments.
    """
    parser = argparse.ArgumentParser(description='Test VM injection using Vagrant commands.')
    parser.add_argument('--source-path', type=str, default='test_file.txt',
                        help='The path to the source file.')
    parser.add_argument('--target-path', type=str, default='/tmp/injection_test/test_file.txt',
                        help='The path to the target file.')
    
    return parser.parse_args()

def main():
    """
    Main function.
    """
    # Parse command line arguments
    args = parse_args()
    
    # Test VM injection
    test_injection(
        source_path=args.source_path,
        target_path=args.target_path
    )

if __name__ == '__main__':
    # Run the main function
    main()
