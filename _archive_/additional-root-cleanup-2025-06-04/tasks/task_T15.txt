# Task T15: Fix Item Model relationships and validation

Status: done
Priority: high

## Description

Update Item model with correct relationships and validation

## Dependencies

- Task T13

## Implementation Details

Fix the Item model to ensure correct relationships with the User model, add proper validation for all fields, implement ownership functionality, and add status tracking.

## Test Strategy

Write tests to verify Item model relationships work correctly. Test validation rules for all fields and ensure ownership and status tracking work as expected.

## Subtasks

- [x] T15.1: Fix item model relationships
- [x] T15.2: Add proper validation
- [x] T15.3: Implement ownership
- [x] T15.4: Add status tracking
