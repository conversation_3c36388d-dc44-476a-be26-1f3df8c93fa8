#!/bin/bash

# This script runs the Playwright tests for the file upload functionality

# Set working directory to the application root
cd "$(dirname "$0")/.." || exit 1

echo "===== Starting test runner ====="

# Check if we should run the development server tests
if [[ "$1" == "--with-dev-server" ]]; then
  echo "Will run development server tests"
  RUN_DEV_SERVER_TESTS=true
else
  echo "Skipping development server tests (use --with-dev-server to include them)"
  RUN_DEV_SERVER_TESTS=false
fi

# Install dependencies if needed
if [[ ! -d "node_modules" ]]; then
  echo "Installing dependencies..."
  npm install
fi

# Install Playwright browsers if needed
if [[ ! -d "node_modules/.cache/ms-playwright" ]]; then
  echo "Installing Playwright browsers..."
  npx playwright install
fi

# Run the regular tests first
echo "===== Running regular file upload tests ====="
npx playwright test tests/playwright/file-upload.spec.ts tests/playwright/file-upload-edge-cases.spec.ts tests/playwright/file-upload-accessibility.spec.ts tests/playwright/file-upload-performance.spec.ts tests/playwright/file-upload-security.spec.ts

# Check if the tests passed
REGULAR_TESTS_EXIT_CODE=$?
if [[ $REGULAR_TESTS_EXIT_CODE -ne 0 ]]; then
  echo "❌ Regular tests failed with exit code $REGULAR_TESTS_EXIT_CODE"
else
  echo "✅ Regular tests passed"
fi

# Run development server tests if requested
if [[ "$RUN_DEV_SERVER_TESTS" == "true" ]]; then
  echo "===== Running development server tests ====="
  # Run the dev server tests separately since they need to manage the server
  npx playwright test tests/playwright/dev-server-startup.spec.ts
  
  # Check if the dev server tests passed
  DEV_SERVER_TESTS_EXIT_CODE=$?
  if [[ $DEV_SERVER_TESTS_EXIT_CODE -ne 0 ]]; then
    echo "❌ Development server tests failed with exit code $DEV_SERVER_TESTS_EXIT_CODE"
  else
    echo "✅ Development server tests passed"
  fi
  
  # Determine the overall exit code
  if [[ $REGULAR_TESTS_EXIT_CODE -ne 0 || $DEV_SERVER_TESTS_EXIT_CODE -ne 0 ]]; then
    echo "❌ Some tests failed"
    exit 1
  else
    echo "✅ All tests passed"
    exit 0
  fi
else
  # Exit with the regular tests exit code
  if [[ $REGULAR_TESTS_EXIT_CODE -ne 0 ]]; then
    echo "❌ Tests failed"
    exit $REGULAR_TESTS_EXIT_CODE
  else
    echo "✅ All tests passed"
    exit 0
  fi
fi 