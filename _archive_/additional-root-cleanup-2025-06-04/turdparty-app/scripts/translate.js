#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Supported languages
const LANGUAGES = [
  'en_GB', 'de', 'fr', 'es', 'it', 'nl', 'pl', 'pt', 'ru', 'ja', 'zh',
  'cs', 'sv', 'da', 'fi', 'el', 'hu', 'ro', 'tr', 'uk', 'bg', 'et',
  'lv', 'lt', 'sl', 'sk', 'af', 'zu'
];

// Base language (source of truth)
const BASE_LANGUAGE = 'en_GB';

// Parse command line arguments
const args = process.argv.slice(2);
let component = null;
let language = null;

for (let i = 0; i < args.length; i++) {
  if (args[i] === '--component' || args[i] === '-c') {
    component = args[i + 1];
    i++;
  } else if (args[i] === '--language' || args[i] === '-l') {
    language = args[i + 1];
    i++;
  } else if (args[i].startsWith('--component=')) {
    component = args[i].split('=')[1];
  } else if (args[i].startsWith('--language=')) {
    language = args[i].split('=')[1];
  }
}

// Validate language if specified
if (language && !LANGUAGES.includes(language)) {
  console.error(`Error: Language '${language}' is not supported.`);
  console.error(`Supported languages: ${LANGUAGES.join(', ')}`);
  process.exit(1);
}

// Root directory for translations
const LANG_DIR = path.join(process.cwd(), 'lang');

// Ensure the language directories exist
function ensureLanguageDirectories() {
  console.log('Ensuring language directories exist...');
  
  LANGUAGES.forEach(lang => {
    const langDir = path.join(LANG_DIR, lang);
    
    if (!fs.existsSync(langDir)) {
      console.log(`Creating directory for ${lang}...`);
      fs.mkdirSync(langDir, { recursive: true });
    }
    
    // Create subdirectories
    ['ui/components', 'ui/pages', 'api'].forEach(subdir => {
      const subdirPath = path.join(langDir, subdir);
      if (!fs.existsSync(subdirPath)) {
        fs.mkdirSync(subdirPath, { recursive: true });
      }
    });
  });
}

// Create or update a translation file
function createOrUpdateTranslation(sourcePath, targetPath, sourceContent) {
  // If target file exists, merge with source
  if (fs.existsSync(targetPath)) {
    const targetContent = JSON.parse(fs.readFileSync(targetPath, 'utf8'));
    const mergedContent = { ...sourceContent };
    
    // Keep existing translations, add new keys
    Object.keys(sourceContent).forEach(key => {
      if (targetContent[key]) {
        mergedContent[key] = targetContent[key];
      }
    });
    
    fs.writeFileSync(targetPath, JSON.stringify(mergedContent, null, 2));
    console.log(`Updated ${targetPath}`);
  } else {
    // Create new file with machine translation
    const translatedContent = machineTranslate(sourceContent, path.basename(path.dirname(targetPath)), path.basename(targetPath, '.json'));
    fs.writeFileSync(targetPath, JSON.stringify(translatedContent, null, 2));
    console.log(`Created ${targetPath}`);
  }
}

// Simple mock machine translation (in a real app, this would use a translation API)
function machineTranslate(sourceContent, targetLang, namespace) {
  // This is a mock function - in a real app, you would call a translation API
  console.log(`[Mock] Translating ${namespace} to ${targetLang}...`);
  
  // For demonstration, we'll just prefix the strings with the language code
  const translatedContent = {};
  Object.keys(sourceContent).forEach(key => {
    // Keep special formatting for plurals and parameters
    if (typeof sourceContent[key] === 'string') {
      if (sourceContent[key].includes('{count, plural,')) {
        // Keep plural format
        translatedContent[key] = sourceContent[key];
      } else if (sourceContent[key].includes('{')) {
        // Keep parameter format but translate the text
        translatedContent[key] = `[${targetLang}] ${sourceContent[key]}`;
      } else {
        // Simple translation
        translatedContent[key] = `[${targetLang}] ${sourceContent[key]}`;
      }
    } else {
      // Keep non-string values as is
      translatedContent[key] = sourceContent[key];
    }
  });
  
  return translatedContent;
}

// Process a specific component
function processComponent(componentName) {
  console.log(`Processing component: ${componentName}`);
  
  // Source file path (English)
  const sourceDir = path.join(LANG_DIR, BASE_LANGUAGE, 'ui', 'components');
  const sourceFile = path.join(sourceDir, `${componentName}.json`);
  
  if (!fs.existsSync(sourceFile)) {
    console.error(`Error: Source file ${sourceFile} does not exist.`);
    process.exit(1);
  }
  
  // Read source content
  const sourceContent = JSON.parse(fs.readFileSync(sourceFile, 'utf8'));
  
  // Process each language
  LANGUAGES.forEach(lang => {
    if (lang === BASE_LANGUAGE) return; // Skip base language
    
    const targetDir = path.join(LANG_DIR, lang, 'ui', 'components');
    const targetFile = path.join(targetDir, `${componentName}.json`);
    
    createOrUpdateTranslation(sourceFile, targetFile, sourceContent);
  });
}

// Process all components
function processAllComponents() {
  console.log('Processing all components...');
  
  // Get all component files from the base language
  const baseComponentsDir = path.join(LANG_DIR, BASE_LANGUAGE, 'ui', 'components');
  
  if (!fs.existsSync(baseComponentsDir)) {
    console.error(`Error: Base components directory ${baseComponentsDir} does not exist.`);
    // Create the directory
    fs.mkdirSync(baseComponentsDir, { recursive: true });
    console.log(`Created directory ${baseComponentsDir}`);
    return;
  }
  
  const componentFiles = fs.readdirSync(baseComponentsDir)
    .filter(file => file.endsWith('.json'));
  
  if (componentFiles.length === 0) {
    console.log('No component files found. Creating sample component file...');
    createSampleComponentFile();
    return;
  }
  
  componentFiles.forEach(file => {
    const componentName = path.basename(file, '.json');
    processComponent(componentName);
  });
}

// Create a sample component file
function createSampleComponentFile() {
  const sampleDir = path.join(LANG_DIR, BASE_LANGUAGE, 'ui', 'components');
  const sampleFile = path.join(sampleDir, 'common.json');
  
  const sampleContent = {
    "app.title": "TurdParty Application",
    "app.description": "A comprehensive application for file upload, VM management, and injection operations",
    "app.welcome": "Welcome to TurdParty, {name}!",
    "app.items": "{count, plural, one {# item} other {# items}}",
    "nav.home": "Home",
    "nav.files": "Files",
    "nav.vms": "Virtual Machines",
    "nav.injections": "Injections",
    "nav.status": "VM Status",
    "nav.docs": "Documentation",
    "button.create": "Create",
    "button.edit": "Edit",
    "button.delete": "Delete",
    "button.cancel": "Cancel",
    "button.save": "Save",
    "button.refresh": "Refresh"
  };
  
  fs.writeFileSync(sampleFile, JSON.stringify(sampleContent, null, 2));
  console.log(`Created sample component file: ${sampleFile}`);
  
  // Now process this component
  processComponent('common');
}

// Create a sample VM status component file
function createVmStatusComponentFile() {
  const sampleDir = path.join(LANG_DIR, BASE_LANGUAGE, 'ui', 'components');
  const sampleFile = path.join(sampleDir, 'vm_status.json');
  
  const sampleContent = {
    "vm_status.title": "VM Status Dashboard",
    "vm_status.loading": "Loading VM data...",
    "vm_status.no_vms": "No VMs found",
    "vm_status.select_vm": "Select a VM from the list to view details",
    "vm_status.vm_list": "Virtual Machines",
    "vm_status.overview": "Overview",
    "vm_status.injections": "Injections",
    "vm_status.logs": "Logs",
    "vm_status.vm_info": "VM Information",
    "vm_status.resource_usage": "Resource Usage",
    "vm_status.cpu_usage": "CPU Usage",
    "vm_status.memory_usage": "Memory Usage",
    "vm_status.disk_usage": "Disk Usage",
    "vm_status.no_resource_data": "Resource usage data not available",
    "vm_status.file_injections": "File Injections",
    "vm_status.no_injections": "No injections found for this VM",
    "vm_status.vm_logs": "VM Logs",
    "vm_status.no_logs": "No logs available for this VM",
    "vm_status.description": "Description",
    "vm_status.no_description": "No description",
    "vm_status.additional_command": "Additional Command",
    "vm_status.status.running": "RUNNING",
    "vm_status.status.stopped": "STOPPED",
    "vm_status.status.starting": "STARTING",
    "vm_status.status.stopping": "STOPPING",
    "vm_status.status.provisioning": "PROVISIONING",
    "vm_status.status.error": "ERROR",
    "vm_status.injection.completed": "COMPLETED",
    "vm_status.injection.failed": "FAILED",
    "vm_status.injection.pending": "PENDING",
    "vm_status.injection.in_progress": "IN PROGRESS"
  };
  
  fs.writeFileSync(sampleFile, JSON.stringify(sampleContent, null, 2));
  console.log(`Created VM status component file: ${sampleFile}`);
  
  // Now process this component
  processComponent('vm_status');
}

// Process a specific language
function processLanguage(lang) {
  console.log(`Processing language: ${lang}`);
  
  // Get all component files from the base language
  const baseComponentsDir = path.join(LANG_DIR, BASE_LANGUAGE, 'ui', 'components');
  
  if (!fs.existsSync(baseComponentsDir)) {
    console.error(`Error: Base components directory ${baseComponentsDir} does not exist.`);
    // Create the directory
    fs.mkdirSync(baseComponentsDir, { recursive: true });
    console.log(`Created directory ${baseComponentsDir}`);
    
    // Create sample files
    createSampleComponentFile();
    createVmStatusComponentFile();
    return;
  }
  
  const componentFiles = fs.readdirSync(baseComponentsDir)
    .filter(file => file.endsWith('.json'));
  
  if (componentFiles.length === 0) {
    console.log('No component files found. Creating sample component files...');
    createSampleComponentFile();
    createVmStatusComponentFile();
    return;
  }
  
  // Process each component for this language
  componentFiles.forEach(file => {
    const componentName = path.basename(file, '.json');
    const sourceFile = path.join(baseComponentsDir, file);
    const sourceContent = JSON.parse(fs.readFileSync(sourceFile, 'utf8'));
    
    const targetDir = path.join(LANG_DIR, lang, 'ui', 'components');
    const targetFile = path.join(targetDir, file);
    
    createOrUpdateTranslation(sourceFile, targetFile, sourceContent);
  });
  
  // Also process common files
  const baseCommonFile = path.join(LANG_DIR, BASE_LANGUAGE, 'ui', 'common.json');
  if (fs.existsSync(baseCommonFile)) {
    const sourceContent = JSON.parse(fs.readFileSync(baseCommonFile, 'utf8'));
    const targetFile = path.join(LANG_DIR, lang, 'ui', 'common.json');
    createOrUpdateTranslation(baseCommonFile, targetFile, sourceContent);
  }
}

// Main function
function main() {
  // Ensure directories exist
  ensureLanguageDirectories();
  
  // Process based on arguments
  if (component) {
    processComponent(component);
  } else if (language) {
    processLanguage(language);
  } else {
    // Create sample files if they don't exist
    const baseComponentsDir = path.join(LANG_DIR, BASE_LANGUAGE, 'ui', 'components');
    if (!fs.existsSync(baseComponentsDir) || fs.readdirSync(baseComponentsDir).filter(file => file.endsWith('.json')).length === 0) {
      console.log('Creating sample component files...');
      createSampleComponentFile();
      createVmStatusComponentFile();
    }
    
    processAllComponents();
  }
  
  console.log('Translation process completed successfully!');
}

// Run the main function
main(); 