#!/bin/bash

# Script to capture screenshots of the main functionality using Playwright

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "Error: Node.js is not installed or not in PATH"
    echo "Please install Node.js to run this script:"
    echo "  Ubuntu/Debian: sudo apt update && sudo apt install -y nodejs npm"
    echo "  CentOS/RHEL: sudo yum install -y nodejs npm"
    echo "  macOS: brew install node"
    echo "  Windows: Download from https://nodejs.org/"
    echo ""
    echo "After installing Node.js, install Playwright:"
    echo "  npm install -g playwright"
    echo "  npx playwright install"
    exit 1
fi

# Check if npx is installed
if ! command -v npx &> /dev/null; then
    echo "Error: npx is not installed or not in PATH"
    echo "Please install npm to run this script:"
    echo "  Ubuntu/Debian: sudo apt update && sudo apt install -y npm"
    echo "  CentOS/RHEL: sudo yum install -y npm"
    echo "  macOS: brew install npm"
    echo ""
    exit 1
fi

# Change to the app directory
cd "$(dirname "$0")/.."

# Ensure the screenshots directory exists
mkdir -p docs/screenshots

# Run the authentication setup first
echo "Setting up authentication..."
npx playwright test tests/playwright/auth.setup.ts

# Run the screenshot capture tests
echo "Capturing screenshots..."
npx playwright test tests/playwright/capture-screenshots.spec.ts

echo "Screenshots have been saved to docs/screenshots/"
echo "Note: This directory is excluded from git via .gitignore" 