import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import Backend from 'i18next-http-backend';
import LanguageDetector from 'i18next-browser-languagedetector';

i18n
  // Load translations from backend
  .use(Backend)
  // Detect user language
  .use(LanguageDetector)
  // Pass the i18n instance to react-i18next
  .use(initReactI18next)
  // Initialize i18next
  .init({
    fallbackLng: 'en_GB',
    debug: process.env.NODE_ENV === 'development',
    
    // Backend configuration
    backend: {
      // Path to load resources from
      loadPath: '/lang/{{lng}}/{{ns}}.json',
    },
    
    // Default namespace
    defaultNS: 'common',
    
    // Namespaces to load
    ns: ['common', 'components/common', 'pages/common'],
    
    // Interpolation configuration
    interpolation: {
      // React already escapes values by default
      escapeValue: false,
    },
    
    // React configuration
    react: {
      // Wait for translations to be loaded before rendering
      useSuspense: true,
    },
  });

export default i18n; 