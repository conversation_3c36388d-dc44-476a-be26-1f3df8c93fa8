.vm-injection-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.vm-injection-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.vm-injection-table {
  margin-top: 20px;
}

.vm-injection-alert {
  margin-bottom: 20px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 50vh;
}

/* Table styles */
.vm-injection-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.vm-injection-table .ant-table-tbody > tr:hover > td {
  background-color: #f0f7ff;
}

/* Button spacing in action column */
.vm-injection-table .ant-space {
  gap: 8px;
}

/* Injection Details Modal */
.injection-details {
  margin-top: 16px;
}

.injection-status-card {
  margin-bottom: 16px;
}

.injection-status {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 24px;
}

.injection-status .ant-tag {
  font-size: 14px;
  padding: 4px 8px;
}

.injection-description {
  margin-top: 16px;
}

/* Status tag styling */
.ant-tag .anticon {
  margin-right: 4px;
}

/* Form styles */
.ant-form-item-label > label {
  font-weight: 500;
}

/* Descriptions styling */
.ant-descriptions-bordered .ant-descriptions-item-label {
  background-color: #f5f5f5;
  font-weight: 500;
}

/* Steps styling */
.ant-steps-item-title {
  font-weight: 500;
}

.ant-steps-item-description {
  font-size: 12px;
}

/* Command code styling */
.injection-details code {
  background-color: #f5f5f5;
  padding: 4px 8px;
  border-radius: 4px;
  font-family: monospace;
  display: block;
  white-space: pre-wrap;
}

/* Select dropdown styling */
.ant-select-selection-item {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}
