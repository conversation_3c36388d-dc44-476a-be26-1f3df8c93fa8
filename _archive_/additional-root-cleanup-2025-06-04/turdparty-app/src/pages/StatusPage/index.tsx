import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { 
  Card, 
  Row, 
  Col, 
  Typography, 
  Spin, 
  Alert, 
  Badge, 
  Space, 
  Statistic, 
  Divider, 
  Button, 
  Tooltip,
  Timeline,
  Tabs
} from 'antd';
import { 
  CheckCircleOutlined, 
  CloseCircleOutlined, 
  SyncOutlined, 
  CloudServerOutlined, 
  DatabaseOutlined, 
  ApiOutlined, 
  CloudOutlined, 
  CodeOutlined,
  ReloadOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import './styles.css';
import TestRunViewer from '../../components/TestRunViewer';

const { Title, Text, Paragraph } = Typography;

interface ComponentStatus {
  status: 'ok' | 'error' | 'unknown';
  error?: string;
  [key: string]: any;
}

interface SystemStatus {
  status: 'ok' | 'degraded' | 'error';
  timestamp: string;
  components: {
    api: ComponentStatus;
    database: ComponentStatus;
    remote_server: ComponentStatus & { hostname: string };
    minio: ComponentStatus & { hostname: string; bucket_count?: number; buckets?: any[] };
    vagrant: ComponentStatus & { vm_count?: number; vms?: any[] };
  };
}

const StatusPage: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [systemStatus, setSystemStatus] = useState<SystemStatus | null>(null);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const { token } = useAuth();
  const navigate = useNavigate();

  const fetchSystemStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await axios.get<SystemStatus>('/api/health/system-status', {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });
      
      setSystemStatus(response.data);
    } catch (err) {
      console.error('Error fetching system status:', err);
      setError('Failed to load system status. Please try again later.');
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchSystemStatus();
    setRefreshing(false);
  };

  useEffect(() => {
    fetchSystemStatus();
    
    // Set up auto-refresh every 30 seconds
    const intervalId = setInterval(() => {
      fetchSystemStatus();
    }, 30000);
    
    return () => clearInterval(intervalId);
  }, [token]);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ok':
        return <CheckCircleOutlined style={{ color: '#52c41a' }} />;
      case 'error':
        return <CloseCircleOutlined style={{ color: '#f5222d' }} />;
      case 'degraded':
        return <ExclamationCircleOutlined style={{ color: '#faad14' }} />;
      default:
        return <SyncOutlined spin style={{ color: '#1890ff' }} />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ok':
        return 'success';
      case 'error':
        return 'error';
      case 'degraded':
        return 'warning';
      default:
        return 'processing';
    }
  };

  const getComponentIcon = (component: string) => {
    switch (component) {
      case 'api':
        return <ApiOutlined />;
      case 'database':
        return <DatabaseOutlined />;
      case 'remote_server':
        return <CloudServerOutlined />;
      case 'minio':
        return <CloudOutlined />;
      case 'vagrant':
        return <CodeOutlined />;
      default:
        return <InfoCircleOutlined />;
    }
  };

  if (loading) {
    return (
      <div className="status-page-loading">
        <Spin size="large" />
        <Title level={3} style={{ marginTop: 24 }}>Loading System Status...</Title>
      </div>
    );
  }

  if (error) {
    return (
      <div className="status-page-container">
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          action={
            <Button onClick={handleRefresh} type="primary">
              Try Again
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div className="status-page">
      <Card className="site-page-header">
        <Space direction="vertical" size="small">
          <Title level={2}>System Status</Title>
          <Text type="secondary">View the current system status and test results</Text>
        </Space>
      </Card>

      <Tabs 
        defaultActiveKey="system" 
        className="status-tabs"
        items={[
          {
            key: 'system',
            label: 'System Status',
            children: (
              <div className="status-page-container">
                <div className="status-page-header">
                  <div>
                    <Title level={2}>System Status</Title>
                    <Text type="secondary">
                      Last updated: {systemStatus ? new Date(systemStatus.timestamp).toLocaleString() : 'Unknown'}
                    </Text>
                  </div>
                  <Button 
                    type="primary" 
                    icon={<ReloadOutlined />} 
                    onClick={handleRefresh}
                    loading={refreshing}
                  >
                    Refresh
                  </Button>
                </div>

                {systemStatus && (
                  <>
                    <Card className="status-overview-card">
                      <Row gutter={[24, 24]} align="middle">
                        <Col xs={24} sm={12} md={8}>
                          <div className="status-badge-container">
                            <Badge 
                              status={getStatusColor(systemStatus.status)} 
                              text={
                                <Text strong style={{ fontSize: '16px' }}>
                                  {systemStatus.status === 'ok' ? 'All Systems Operational' : 
                                   systemStatus.status === 'degraded' ? 'Partial System Outage' : 
                                   'Major System Outage'}
                                </Text>
                              } 
                            />
                          </div>
                        </Col>
                        <Col xs={24} sm={12} md={16}>
                          <Row gutter={[16, 16]}>
                            {Object.entries(systemStatus.components).map(([key, component]) => (
                              <Col key={key} xs={12} sm={8} md={6}>
                                <Tooltip title={component.error || `${key} is ${component.status}`}>
                                  <Badge 
                                    status={getStatusColor(component.status)} 
                                    text={key.replace('_', ' ')}
                                  />
                                </Tooltip>
                              </Col>
                            ))}
                          </Row>
                        </Col>
                      </Row>
                    </Card>

                    <Row gutter={[24, 24]} className="status-cards-container">
                      <Col xs={24} lg={12}>
                        <Card 
                          title={
                            <Space>
                              <CloudServerOutlined />
                              <span>Remote Server</span>
                              <Badge status={getStatusColor(systemStatus.components.remote_server.status)} />
                            </Space>
                          }
                          className="component-card"
                        >
                          <Statistic 
                            title="Hostname" 
                            value={systemStatus.components.remote_server.hostname} 
                          />
                          {systemStatus.components.remote_server.error && (
                            <Alert 
                              message="Connection Error" 
                              description={systemStatus.components.remote_server.error} 
                              type="error" 
                              showIcon 
                              style={{ marginTop: 16 }}
                            />
                          )}
                        </Card>
                      </Col>

                      <Col xs={24} lg={12}>
                        <Card 
                          title={
                            <Space>
                              <CloudOutlined />
                              <span>MinIO Storage</span>
                              <Badge status={getStatusColor(systemStatus.components.minio.status)} />
                            </Space>
                          }
                          className="component-card"
                        >
                          {systemStatus.components.minio.status === 'ok' ? (
                            <>
                              <Row gutter={[16, 16]}>
                                <Col span={12}>
                                  <Statistic 
                                    title="Bucket Count" 
                                    value={systemStatus.components.minio.bucket_count || 0} 
                                  />
                                </Col>
                                <Col span={12}>
                                  <Statistic 
                                    title="Hostname" 
                                    value={systemStatus.components.minio.hostname} 
                                  />
                                </Col>
                              </Row>
                              {systemStatus.components.minio.buckets && systemStatus.components.minio.buckets.length > 0 && (
                                <>
                                  <Divider>Buckets</Divider>
                                  <Timeline
                                    items={systemStatus.components.minio.buckets.map(bucket => ({
                                      color: 'blue',
                                      children: (
                                        <div>
                                          <Text strong>{bucket.name}</Text>
                                          {bucket.created && (
                                            <div>
                                              <Text type="secondary">Created: {new Date(bucket.created).toLocaleString()}</Text>
                                            </div>
                                          )}
                                        </div>
                                      )
                                    }))}
                                  />
                                </>
                              )}
                            </>
                          ) : (
                            <Alert 
                              message="Connection Error" 
                              description={systemStatus.components.minio.error} 
                              type="error" 
                              showIcon 
                            />
                          )}
                        </Card>
                      </Col>

                      <Col xs={24} lg={12}>
                        <Card 
                          title={
                            <Space>
                              <CodeOutlined />
                              <span>Vagrant Service</span>
                              <Badge status={getStatusColor(systemStatus.components.vagrant.status)} />
                            </Space>
                          }
                          className="component-card"
                        >
                          {systemStatus.components.vagrant.status === 'ok' ? (
                            <>
                              <Statistic 
                                title="VM Count" 
                                value={systemStatus.components.vagrant.vm_count || 0} 
                              />
                              {systemStatus.components.vagrant.vms && systemStatus.components.vagrant.vms.length > 0 && (
                                <>
                                  <Divider>Virtual Machines</Divider>
                                  <Timeline
                                    items={systemStatus.components.vagrant.vms.map(vm => ({
                                      color: vm.status === 'running' ? 'green' : 'gray',
                                      children: (
                                        <div>
                                          <Text strong>{vm.name}</Text>
                                          <div>
                                            <Badge 
                                              status={vm.status === 'running' ? 'success' : 'default'} 
                                              text={vm.status} 
                                            />
                                          </div>
                                        </div>
                                      )
                                    }))}
                                  />
                                </>
                              )}
                            </>
                          ) : (
                            <Alert 
                              message="Connection Error" 
                              description={systemStatus.components.vagrant.error} 
                              type="error" 
                              showIcon 
                            />
                          )}
                        </Card>
                      </Col>

                      <Col xs={24} lg={12}>
                        <Card 
                          title={
                            <Space>
                              <DatabaseOutlined />
                              <span>Database</span>
                              <Badge status={getStatusColor(systemStatus.components.database.status)} />
                            </Space>
                          }
                          className="component-card"
                        >
                          {systemStatus.components.database.status === 'ok' ? (
                            <Alert 
                              message="Database Connection" 
                              description="Database is connected and operational." 
                              type="success" 
                              showIcon 
                            />
                          ) : (
                            <Alert 
                              message="Database Error" 
                              description={systemStatus.components.database.error || "Unknown database error"} 
                              type="error" 
                              showIcon 
                            />
                          )}
                        </Card>
                      </Col>
                    </Row>
                  </>
                )}
              </div>
            )
          },
          {
            key: 'tests',
            label: 'Test Results',
            children: <TestRunViewer onRefresh={fetchSystemStatus} />
          }
        ]}
      />
    </div>
  );
};

export default StatusPage; 