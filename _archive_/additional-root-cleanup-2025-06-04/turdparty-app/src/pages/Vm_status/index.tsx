import React, { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import axios from 'axios';
import './styles.css';
import { 
  Table, Button, Spin, Alert, Card, Typography, Descriptions, 
  Tag, Space, Statistic, Tabs, Badge, Divider, Progress, Timeline,
  notification, Skeleton, Empty
} from 'antd';
import { 
  DesktopOutlined, CheckCircleOutlined, CloseCircleOutlined, 
  SyncOutlined, ClockCircleOutlined, InfoCircleOutlined,
  FileOutlined, CodeOutlined, ReloadOutlined, EyeOutlined,
  StopOutlined, WarningOutlined
} from '@ant-design/icons';
import { useAuth } from '../../hooks/useAuth';
import API_ENDPOINTS from '../../utils/apiConfig';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// Constants
const REFRESH_INTERVAL = 5000; // 5 seconds
const API_TIMEOUT = 8000; // 8 seconds
const MAX_RETRIES = 3;

// Interfaces for data types
interface VMInfo {
  id: string;
  name: string;
  description: string;
  template: string;
  memory_mb: number;
  cpus: number;
  disk_gb: number;
  status: string;
  ip_address: string | null;
  ssh_port: number | null;
  vagrant_id: string | null;
  created_on: string;
  modified_on: string | null;
  owner_id: string;
  last_action: string | null;
  last_action_time: string | null;
  error_message: string | null;
}

interface VMInjectionInfo {
  id: string;
  vagrant_vm_id: string;
  file_selection_id: string;
  description: string | null;
  status: string;
  additional_command: string | null;
  error_message: string | null;
  created_on: string;
  modified_on: string | null;
  completed_on: string | null;
  owner_id: string;
  file_info: {
    id: string;
    filename: string;
    file_size: number;
    content_type: string;
    download_url: string;
  } | null;
}

interface VMLogEntry {
  timestamp: string;
  level: string;
  message: string;
}

interface VMResourceUsage {
  cpu_percent: number;
  memory_used_mb: number;
  memory_total_mb: number;
  disk_used_gb: number;
  disk_total_gb: number;
}

// Static translations
const translations = {
  vm_status: {
    title: 'VM Status',
    refresh: 'Refresh',
    vm_list: 'VM List',
    no_vms: 'No VMs found',
    select_vm: 'Select a VM to view details',
    loading: 'Loading VM data...',
    overview: 'Overview',
    injections: 'Injections',
    logs: 'Logs',
    no_logs: 'No logs available',
    api_error: 'API connection error',
    retry_connection: 'Retry Connection',
    retrying: 'Retrying connection...',
    fallback_message: 'Using cached data (API unreachable)',
    description: 'Description',
    no_description: 'No description',
    additional_command: 'Additional Command',
    vm_info: 'VM Information',
    resource_usage: 'Resource Usage',
    cpu_usage: 'CPU Usage',
    memory_usage: 'Memory Usage',
    disk_usage: 'Disk Usage',
    no_resource_data: 'Resource usage data not available',
    file_injections: 'File Injections',
    no_injections: 'No injections found for this VM',
    vm_logs: 'VM Logs',
    status: {
      running: 'RUNNING',
      stopped: 'STOPPED',
      starting: 'STARTING',
      stopping: 'STOPPING',
      provisioning: 'PROVISIONING',
      error: 'ERROR'
    },
    injection: {
      completed: 'COMPLETED',
      failed: 'FAILED',
      pending: 'PENDING',
      in_progress: 'IN PROGRESS'
    }
  }
};

// Translation helper function
const t = (key: string): string => {
  const keys = key.split('.');
  let result: any = translations;
  
  for (const k of keys) {
    if (!result) return key;
    result = result[k];
  }
  
  return result as string || key;
};

// Skeleton loaders for better UX during loading states
const SkeletonLoader: React.FC<{rows?: number; active?: boolean}> = ({rows = 3, active = true}) => (
  <div className="skeleton-loader">
    {Array.from({length: rows}).map((_, i) => (
      <Skeleton key={i} active={active} paragraph={{ rows: 0 }} />
    ))}
  </div>
);

// Memoized resource usage placeholder to improve performance
const ResourceUsagePlaceholder = React.memo(() => (
  <div className="resource-usage-container">
    {[1, 2, 3].map((item) => (
      <div key={item} className="resource-usage-item">
        <Skeleton active paragraph={{ rows: 2 }} />
      </div>
    ))}
  </div>
));

// Main component
const VmStatusPage: React.FC = () => {
  // State
  const [vms, setVMs] = useState<VMInfo[]>([]);
  const [selectedVM, setSelectedVM] = useState<VMInfo | null>(null);
  const [injections, setInjections] = useState<VMInjectionInfo[]>([]);
  const [logs, setLogs] = useState<VMLogEntry[]>([]);
  const [resourceUsage, setResourceUsage] = useState<VMResourceUsage | null>(null);
  const [initialLoading, setInitialLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [apiConnected, setApiConnected] = useState(true);
  const [retrying, setRetrying] = useState(false);
  const [apiRetryCount, setApiRetryCount] = useState(0);
  const [activeTab, setActiveTab] = useState("overview");
  
  // Refs
  const mountedRef = useRef(true);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  
  // Cache refs to prevent unnecessary updates
  const prevDataCache = useRef({
    vms: [] as VMInfo[],
    selectedVM: null as VMInfo | null,
    injections: [] as VMInjectionInfo[],
    logs: [] as VMLogEntry[],
    resourceUsage: null as VMResourceUsage | null
  });
  
  const { token } = useAuth();

  // State update helpers
  const updateIfMounted = useCallback((update: () => void) => {
    if (mountedRef.current) {
      update();
    }
  }, []);
  
  const shouldUpdate = useCallback(<T,>(newData: T, oldData: T): boolean => {
    if (!newData && !oldData) return false;
    if (!newData && oldData) return false;
    if (newData && !oldData) return true;
    
    return JSON.stringify(newData) !== JSON.stringify(oldData);
  }, []);
  
  // Show error notification
  const showErrorNotification = useCallback((message: string, description: string) => {
    if (!mountedRef.current) return;
    
    notification.error({
      message,
      description,
      duration: 5,
      icon: <WarningOutlined style={{ color: '#ff4d4f' }} />,
      placement: 'topRight'
    });
  }, []);

  // Log errors to console for debugging
  const logError = useCallback((context: string, error: any) => {
    const errorDetails = {
      context,
      message: error?.message || 'Unknown error',
      code: error?.code,
      status: error?.response?.status,
      stack: error?.stack,
      timestamp: new Date().toISOString()
    };
    
    console.error(`[VmStatusPage] Error in ${context}:`, errorDetails);
  }, []);

  // Fetch VM list from API
  const fetchVMs = useCallback(async (isInitialFetch = false) => {
    if (!mountedRef.current) return;
    
    try {
      // Set loading state
      updateIfMounted(() => {
        if (isInitialFetch) {
          setInitialLoading(true);
        } else if (!refreshing) {
          setRefreshing(true);
        }
      });
      
      // Cancel previous requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      // Setup new request controller
      abortControllerRef.current = new AbortController();
      
      // Try primary endpoint
      let response;
      try {
        response = await axios.get(API_ENDPOINTS.VAGRANT_VM.BASE, {
          headers: { Authorization: `Bearer ${token}` },
          timeout: API_TIMEOUT,
          signal: abortControllerRef.current.signal
        });
        
        // Connection successful
        updateIfMounted(() => {
          setApiConnected(true);
          setApiRetryCount(0);
        });
      } catch (err: any) {
        // Try fallback endpoint if main fails
        if (process.env.REACT_APP_API_URL) {
          const fallbackUrl = `${process.env.REACT_APP_API_URL}/api/v1/vagrant_vm/`;
          
          response = await axios.get(fallbackUrl, {
            headers: { Authorization: `Bearer ${token}` },
            timeout: API_TIMEOUT,
            signal: abortControllerRef.current.signal
          });
          
          // Fallback successful
          updateIfMounted(() => {
            setApiConnected(true);
            setApiRetryCount(0);
          });
        } else {
          throw err;
        }
      }
      
      // Process response
      if (response?.data?.items) {
        const newVMs = response.data.items;
        prevDataCache.current.vms = vms;
        
        if (shouldUpdate(newVMs, vms)) {
          updateIfMounted(() => setVMs(newVMs));
          
          // Select first VM if none selected
          if (newVMs.length > 0 && !selectedVM) {
            updateIfMounted(() => setSelectedVM(newVMs[0]));
          } else if (selectedVM) {
            // Update selected VM if it's in the list
            const updatedVM = newVMs.find((vm: VMInfo) => vm.id === selectedVM.id);
            if (updatedVM && shouldUpdate(updatedVM, selectedVM)) {
              prevDataCache.current.selectedVM = selectedVM;
              updateIfMounted(() => setSelectedVM(updatedVM));
            }
          }
        }
      }
      
      // Clear any previous errors
      updateIfMounted(() => setError(null));
    } catch (err: any) {
      // Handle errors appropriately
      if (err.name !== 'AbortError' && mountedRef.current) {
        logError('fetchVMs', err);
        
        if (err.code === 'ECONNABORTED' || err.message?.includes('Network Error') || !err.response) {
          updateIfMounted(() => {
            setApiConnected(false);
            
            if (apiRetryCount < MAX_RETRIES) {
              setApiRetryCount(prev => prev + 1);
              setError('Network error: Cannot connect to the API. Will retry automatically.');
            } else {
              setError('Failed to connect to the API after several attempts. Using cached data.');
            }
          });
        } else {
          updateIfMounted(() => {
            setError(`Error: ${err.response?.status || ''} ${err.message || 'Unknown error'}`);
          });
        }
      }
    } finally {
      // Reset loading state with delay for smoother transitions
      if (mountedRef.current) {
        if (isInitialFetch) {
          updateIfMounted(() => setInitialLoading(false));
        } else {
          setTimeout(() => {
            updateIfMounted(() => setRefreshing(false));
          }, 300);
        }
      }
    }
  }, [token, vms, selectedVM, apiRetryCount, refreshing, updateIfMounted, shouldUpdate, logError]);

  // Fetch VM details from API
  const fetchVMDetails = useCallback(async (vmId: string, isInitialFetch = false) => {
    if (!vmId || !mountedRef.current) return;
    
    try {
      // Set loading state
      updateIfMounted(() => {
        if (isInitialFetch && !initialLoading) {
          setInitialLoading(true);
        } else if (!refreshing) {
          setRefreshing(true);
        }
      });
      
      // Cancel previous requests
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
      
      // Setup new request controller
      abortControllerRef.current = new AbortController();
      
      // Prepare axios config
      const axiosConfig = {
        headers: { Authorization: `Bearer ${token}` },
        timeout: API_TIMEOUT,
        signal: abortControllerRef.current.signal
      };
      
      // Fetch all data in parallel
      const [vmResponse, injectionsResponse, logsResponse, resourceResponse] = await Promise.allSettled([
        axios.get(API_ENDPOINTS.VAGRANT_VM.BY_ID(vmId), {
          ...axiosConfig,
          params: {} // Ensure no conflicts with query params
        }),
        axios.get(API_ENDPOINTS.VM_INJECTION.BASE, {
          ...axiosConfig,
          params: { vagrant_vm_id: vmId }
        }),
        axios.get(API_ENDPOINTS.VAGRANT_VM.LOGS(vmId), axiosConfig),
        axios.get(API_ENDPOINTS.VAGRANT_VM.RESOURCES(vmId), axiosConfig)
      ]);
      
      // VM data fetched successfully
      if (vmResponse.status === 'fulfilled') {
        updateIfMounted(() => {
          setApiConnected(true);
          setApiRetryCount(0);
        });
      }
      
      // Cache current data for comparison
      prevDataCache.current = {
        ...prevDataCache.current,
        selectedVM,
        injections,
        logs,
        resourceUsage
      };
      
      // Update VM details if changed
      if (vmResponse.status === 'fulfilled' && mountedRef.current) {
        const newVMData = vmResponse.value.data;
        
        if (shouldUpdate(newVMData, selectedVM)) {
          updateIfMounted(() => {
            setSelectedVM(newVMData);
            setVMs(prev => prev.map(vm => vm.id === vmId ? newVMData : vm));
          });
        }
      }
      
      // Update injections if changed
      if (injectionsResponse.status === 'fulfilled' && mountedRef.current) {
        const newInjections = injectionsResponse.value.data.items || [];
        
        if (shouldUpdate(newInjections, injections)) {
          updateIfMounted(() => setInjections(newInjections));
        }
      }
      
      // Update logs if changed
      if (logsResponse.status === 'fulfilled' && mountedRef.current) {
        const newLogs = logsResponse.value.data.logs || [];
        
        if (shouldUpdate(newLogs, logs)) {
          updateIfMounted(() => setLogs(newLogs));
        }
      }
      
      // Update resource usage if changed
      if (resourceResponse.status === 'fulfilled' && mountedRef.current) {
        const newResourceUsage = resourceResponse.value.data;
        
        if (shouldUpdate(newResourceUsage, resourceUsage)) {
          updateIfMounted(() => setResourceUsage(newResourceUsage));
        }
      }
      
      // Handle network errors
      const allNetworkErrors = [vmResponse, injectionsResponse, logsResponse, resourceResponse].every(
        result => 
          result.status === 'rejected' && 
          (result.reason.code === 'ECONNABORTED' || 
           result.reason.message?.includes('Network Error') ||
           !result.reason.response)
      );
      
      if (allNetworkErrors && mountedRef.current) {
        updateIfMounted(() => {
          setApiConnected(false);
          
          if (apiRetryCount < MAX_RETRIES) {
            setApiRetryCount(prev => prev + 1);
            setError('Network error: Cannot connect to the API. Will retry automatically.');
          } else {
            setError('Failed to connect to the API after several attempts. Using cached data.');
          }
        });
      } else if (mountedRef.current) {
        updateIfMounted(() => setError(null));
      }
    } catch (err: any) {
      // Handle errors
      if (err.name !== 'AbortError' && mountedRef.current) {
        logError('fetchVMDetails', err);
        
        if (err.code === 'ECONNABORTED' || err.message?.includes('Network Error') || !err.response) {
          updateIfMounted(() => {
            setApiConnected(false);
            
            if (apiRetryCount < MAX_RETRIES) {
              setApiRetryCount(prev => prev + 1);
              setError('Network error: Cannot connect to the API. Will retry automatically.');
            } else {
              setError('Failed to connect to the API after several attempts. Using cached data.');
            }
          });
        } else {
          updateIfMounted(() => {
            setError(`Error: ${err.response?.status || ''} ${err.message || 'Unknown error'}`);
          });
        }
      }
    } finally {
      // Reset loading state
      if (mountedRef.current) {
        if (isInitialFetch) {
          updateIfMounted(() => setInitialLoading(false));
        } else {
          setTimeout(() => {
            updateIfMounted(() => setRefreshing(false));
          }, 300);
        }
      }
    }
  }, [
    token, selectedVM, injections, logs, resourceUsage, 
    apiRetryCount, initialLoading, refreshing, 
    updateIfMounted, shouldUpdate, logError
  ]);

  // Initialize component on mount
  useEffect(() => {
    mountedRef.current = true;
    fetchVMs(true);
    
    return () => {
      mountedRef.current = false;
      
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
      
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchVMs]);

  // Set up auto-refresh interval
  useEffect(() => {
    if (intervalRef.current) {
      clearInterval(intervalRef.current);
    }
    
    intervalRef.current = setInterval(() => {
      if (!refreshing && mountedRef.current) {
        if (selectedVM) {
          fetchVMDetails(selectedVM.id);
        } else {
          fetchVMs();
        }
      }
    }, REFRESH_INTERVAL);
    
    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [selectedVM, refreshing, fetchVMs, fetchVMDetails]);

  // Fetch details when VM selection changes
  useEffect(() => {
    if (selectedVM && mountedRef.current) {
      fetchVMDetails(selectedVM.id, true);
    }
  }, [selectedVM?.id, fetchVMDetails]);

  // Auto-retry on API disconnection
  useEffect(() => {
    if (!apiConnected && apiRetryCount < MAX_RETRIES && mountedRef.current) {
      const retryTimer = setTimeout(() => {
        if (selectedVM) {
          fetchVMDetails(selectedVM.id);
        } else {
          fetchVMs();
        }
      }, 5000 * Math.pow(2, apiRetryCount)); // Exponential backoff
      
      return () => clearTimeout(retryTimer);
    }
  }, [apiConnected, apiRetryCount, fetchVMs, fetchVMDetails, selectedVM]);

  // Handler for manual retry button
  const handleRetryConnection = useCallback(() => {
    if (!mountedRef.current) return;
    
    updateIfMounted(() => {
      setApiRetryCount(0);
      setRetrying(true);
      setError('Retrying connection to API...');
    });
    
    setTimeout(() => {
      if (selectedVM) {
        fetchVMDetails(selectedVM.id);
      } else {
        fetchVMs();
      }
      
      updateIfMounted(() => {
        setRetrying(false);
      });
    }, 300);
  }, [selectedVM, fetchVMs, fetchVMDetails, updateIfMounted]);

  // Handler for manual refresh button
  const handleRefresh = useCallback(() => {
    if (!mountedRef.current || refreshing) return;
    
    if (selectedVM) {
      fetchVMDetails(selectedVM.id);
    } else {
      fetchVMs();
    }
  }, [selectedVM, refreshing, fetchVMs, fetchVMDetails]);

  // Display helpers
  const getStatusColor = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'running': return 'green';
      case 'stopped': return 'red';
      case 'starting':
      case 'stopping':
      case 'provisioning': return 'blue';
      case 'error': return 'red';
      default: return 'default';
    }
  };

  const getStatusIcon = (status: string): React.ReactNode => {
    switch (status?.toLowerCase()) {
      case 'running': return <CheckCircleOutlined />;
      case 'stopped': return <StopOutlined />;
      case 'starting':
      case 'stopping':
      case 'provisioning': return <SyncOutlined spin />;
      case 'error': return <CloseCircleOutlined />;
      default: return <InfoCircleOutlined />;
    }
  };

  const getInjectionStatusColor = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'completed': return 'green';
      case 'failed': return 'red';
      case 'pending':
      case 'in_progress': return 'blue';
      default: return 'default';
    }
  };

  const getInjectionStatusIcon = (status: string): React.ReactNode => {
    switch (status?.toLowerCase()) {
      case 'completed': return <CheckCircleOutlined />;
      case 'failed': return <CloseCircleOutlined />;
      case 'pending': return <ClockCircleOutlined />;
      case 'in_progress': return <SyncOutlined spin />;
      default: return <InfoCircleOutlined />;
    }
  };

  const getTranslatedStatus = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'running': return t('vm_status.status.running');
      case 'stopped': return t('vm_status.status.stopped');
      case 'starting': return t('vm_status.status.starting');
      case 'stopping': return t('vm_status.status.stopping');
      case 'provisioning': return t('vm_status.status.provisioning');
      case 'error': return t('vm_status.status.error');
      default: return status?.toUpperCase() || '';
    }
  };

  const getTranslatedInjectionStatus = (status: string): string => {
    switch (status?.toLowerCase()) {
      case 'completed': return t('vm_status.injection.completed');
      case 'failed': return t('vm_status.injection.failed');
      case 'pending': return t('vm_status.injection.pending');
      case 'in_progress': return t('vm_status.injection.in_progress');
      default: return status?.toUpperCase() || '';
    }
  };

  // Tab Renderers
  const renderOverviewTab = () => {
    if (!selectedVM) return <Alert message={t('vm_status.select_vm')} type="info" />;
    
    return (
      <div className="vm-status-overview">
        <Card className="vm-status-card">
          {refreshing && (
            <div className="content-placeholder">
              <Spin size="small" />
            </div>
          )}
          <Descriptions title={t('vm_status.vm_info')} bordered>
            <Descriptions.Item label="Name">{selectedVM.name}</Descriptions.Item>
            <Descriptions.Item label="Status">
              <Tag icon={getStatusIcon(selectedVM.status)} color={getStatusColor(selectedVM.status)}>
                {getTranslatedStatus(selectedVM.status)}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Template">{selectedVM.template}</Descriptions.Item>
            <Descriptions.Item label="IP Address">{selectedVM.ip_address || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="SSH Port">{selectedVM.ssh_port || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Created On">{new Date(selectedVM.created_on).toLocaleString()}</Descriptions.Item>
            <Descriptions.Item label="Last Action">{selectedVM.last_action || 'N/A'}</Descriptions.Item>
            <Descriptions.Item label="Last Action Time">
              {selectedVM.last_action_time ? new Date(selectedVM.last_action_time).toLocaleString() : 'N/A'}
            </Descriptions.Item>
            <Descriptions.Item label={t('vm_status.description')} span={3}>
              {selectedVM.description || t('vm_status.no_description')}
            </Descriptions.Item>
            {selectedVM.error_message && (
              <Descriptions.Item label="Error Message" span={3}>
                <Alert message={selectedVM.error_message} type="error" />
              </Descriptions.Item>
            )}
          </Descriptions>
        </Card>
        
        <Divider />
        
        <Card className="vm-status-card">
          <Title level={4}>{t('vm_status.resource_usage')}</Title>
          
          {resourceUsage ? (
            <div className="resource-usage-container">
              <div className="resource-usage-item">
                <Statistic 
                  title={t('vm_status.cpu_usage')}
                  value={resourceUsage.cpu_percent}
                  suffix="%" 
                  precision={1}
                />
                <Progress 
                  percent={resourceUsage.cpu_percent} 
                  status={resourceUsage.cpu_percent > 90 ? "exception" : "normal"}
                  strokeColor={
                    resourceUsage.cpu_percent > 90 ? "#f5222d" : 
                    resourceUsage.cpu_percent > 70 ? "#faad14" : "#52c41a"
                  }
                />
              </div>
              
              <div className="resource-usage-item">
                <Statistic 
                  title={t('vm_status.memory_usage')}
                  value={(resourceUsage.memory_used_mb / resourceUsage.memory_total_mb * 100)}
                  suffix="%" 
                  precision={1}
                />
                <Progress 
                  percent={(resourceUsage.memory_used_mb / resourceUsage.memory_total_mb * 100)} 
                  status={(resourceUsage.memory_used_mb / resourceUsage.memory_total_mb * 100) > 90 ? "exception" : "normal"}
                  strokeColor={
                    (resourceUsage.memory_used_mb / resourceUsage.memory_total_mb * 100) > 90 ? "#f5222d" : 
                    (resourceUsage.memory_used_mb / resourceUsage.memory_total_mb * 100) > 70 ? "#faad14" : "#52c41a"
                  }
                />
                <div style={{ textAlign: 'center', fontSize: '12px', marginTop: '4px' }}>
                  {Math.round(resourceUsage.memory_used_mb)} MB / {Math.round(resourceUsage.memory_total_mb)} MB
                </div>
              </div>
              
              <div className="resource-usage-item">
                <Statistic 
                  title={t('vm_status.disk_usage')}
                  value={(resourceUsage.disk_used_gb / resourceUsage.disk_total_gb * 100)}
                  suffix="%" 
                  precision={1}
                />
                <Progress 
                  percent={(resourceUsage.disk_used_gb / resourceUsage.disk_total_gb * 100)} 
                  status={(resourceUsage.disk_used_gb / resourceUsage.disk_total_gb * 100) > 90 ? "exception" : "normal"}
                  strokeColor={
                    (resourceUsage.disk_used_gb / resourceUsage.disk_total_gb * 100) > 90 ? "#f5222d" : 
                    (resourceUsage.disk_used_gb / resourceUsage.disk_total_gb * 100) > 70 ? "#faad14" : "#52c41a"
                  }
                />
                <div style={{ textAlign: 'center', fontSize: '12px', marginTop: '4px' }}>
                  {Math.round(resourceUsage.disk_used_gb * 10) / 10} GB / {Math.round(resourceUsage.disk_total_gb * 10) / 10} GB
                </div>
              </div>
            </div>
          ) : (
            <ResourceUsagePlaceholder />
          )}
        </Card>
      </div>
    );
  };

  const renderInjectionsTab = () => {
    if (!selectedVM) return <Alert message={t('vm_status.select_vm')} type="info" />;
    
    return (
      <div className="vm-status-injections">
        <Card className="vm-status-card">
          <Title level={4}>{t('vm_status.file_injections')}</Title>
          
          {injections && injections.length > 0 ? (
            <>
              {refreshing && (
                <div className="content-placeholder">
                  <Spin size="small" />
                </div>
              )}
              <Table 
                dataSource={injections}
                rowKey="id"
                pagination={false}
                className="injections-table"
                loading={refreshing && injections.length === 0}
              >
                <Table.Column 
                  title="File" 
                  dataIndex="file_info" 
                  key="file_info"
                  render={(fileInfo) => fileInfo ? fileInfo.filename : 'N/A'} 
                />
                <Table.Column 
                  title="Status" 
                  dataIndex="status" 
                  key="status"
                  render={(status) => (
                    <Tag icon={getInjectionStatusIcon(status)} color={getInjectionStatusColor(status)}>
                      {getTranslatedInjectionStatus(status)}
                    </Tag>
                  )} 
                />
                <Table.Column 
                  title="Details" 
                  key="details"
                  render={(record) => (
                    <div>
                      <p><strong>{t('vm_status.description')}:</strong> {record.description || t('vm_status.no_description')}</p>
                      {record.additional_command && (
                        <p><strong>{t('vm_status.additional_command')}:</strong> <code>{record.additional_command}</code></p>
                      )}
                      {record.error_message && (
                        <Alert message={record.error_message} type="error" style={{ marginTop: '8px' }} />
                      )}
                    </div>
                  )} 
                />
              </Table>
            </>
          ) : (
            refreshing ? (
              <SkeletonLoader rows={3} />
            ) : (
              <Alert message={t('vm_status.no_injections')} type="info" />
            )
          )}
        </Card>
      </div>
    );
  };

  const renderLogsTab = () => {
    if (!selectedVM) return <Alert message={t('vm_status.select_vm')} type="info" />;
    
    return (
      <div className="vm-status-logs">
        <Card className="vm-status-card">
          <Title level={4}>{t('vm_status.vm_logs')}</Title>
          
          {logs && logs.length > 0 ? (
            <>
              {refreshing && (
                <div className="content-placeholder">
                  <Spin size="small" />
                </div>
              )}
              <Timeline>
                {logs.map((log, index) => (
                  <Timeline.Item 
                    key={index}
                    color={log.level === 'ERROR' ? 'red' : log.level === 'WARNING' ? 'orange' : 'blue'}
                  >
                    <div className={`log-entry log-${log.level.toLowerCase()}`}>
                      <Text strong>{new Date(log.timestamp).toLocaleString()}</Text> - 
                      <Text type={log.level === 'ERROR' ? 'danger' : log.level === 'WARNING' ? 'warning' : undefined}>
                        [{log.level}] {log.message}
                      </Text>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            </>
          ) : (
            refreshing ? (
              <SkeletonLoader rows={5} />
            ) : (
              <Alert message={t('vm_status.no_logs')} type="info" />
            )
          )}
        </Card>
      </div>
    );
  };

  // Calculate container classes
  const containerClasses = useMemo(() => {
    const classes = ['vm-status-container'];
    if (!apiConnected) classes.push('api-disconnected');
    if (refreshing) classes.push('refresh-loading');
    return classes.join(' ');
  }, [apiConnected, refreshing]);
  
  // Main render
  if (initialLoading) {
    return (
      <div className="vm-status-loading">
        <Spin size="large" />
        <p>{t('vm_status.loading')}</p>
      </div>
    );
  }

  return (
    <div className={containerClasses} data-testid="vm-status-container">
      <div className="vm-status-header">
        <Title level={2}>
          <DesktopOutlined /> {t('vm_status.title')}
          {!apiConnected && (
            <Tag color="red" style={{ marginLeft: 12 }}>
              API Offline
            </Tag>
          )}
        </Title>
        
        <Space>
          {!apiConnected && (
            <Button 
              type="primary" 
              danger
              icon={<SyncOutlined spin={retrying} />} 
              onClick={handleRetryConnection}
              loading={retrying}
            >
              {t('vm_status.retry_connection')}
            </Button>
          )}
          <Button 
            type="primary" 
            icon={<ReloadOutlined spin={refreshing} />} 
            onClick={handleRefresh}
            loading={refreshing}
            disabled={!apiConnected && apiRetryCount >= MAX_RETRIES}
          >
            {t('vm_status.refresh')}
          </Button>
        </Space>
      </div>
      
      {error && (
        <Alert 
          message={error} 
          type={error.includes('Network error') ? "warning" : "error"} 
          showIcon 
          style={{ marginBottom: '16px' }} 
          action={
            error.includes('Network error') && (
              <Button size="small" type="primary" onClick={handleRetryConnection}>
                Retry Now
              </Button>
            )
          }
        />
      )}
      
      {!apiConnected && apiRetryCount >= MAX_RETRIES && (
        <Alert
          message={t('vm_status.fallback_message')}
          type="warning"
          showIcon
          style={{ marginBottom: '16px' }}
        />
      )}
      
      <div className="vm-status-content">
        <div className="vm-status-sidebar">
          <Card title={t('vm_status.vm_list')} className="vm-list-card">
            {refreshing && (
              <div className={`vm-list-refreshing ${refreshing ? 'active' : ''}`}>
                <Spin size="small" />
              </div>
            )}
            
            {vms && vms.length > 0 ? (
              <div className="vm-list">
                {vms.map((vm) => (
                  <div 
                    key={vm.id}
                    className={`vm-list-item ${selectedVM && selectedVM.id === vm.id ? 'selected' : ''}`}
                    onClick={() => !refreshing && setSelectedVM(vm)}
                  >
                    {getStatusIcon(vm.status)}
                    <div className="vm-list-item-name">
                      {vm.name}
                      {vm.status === 'error' && (
                        <Badge status="error" style={{ marginLeft: '8px' }} />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : refreshing ? (
              <SkeletonLoader rows={4} />
            ) : (
              <Empty 
                description={t('vm_status.no_vms')} 
                image={Empty.PRESENTED_IMAGE_SIMPLE} 
              />
            )}
          </Card>
        </div>
        
        <div className="vm-status-details">
          {selectedVM ? (
            <Card 
              title={
                <div className="vm-status-title">
                  <DesktopOutlined /> {selectedVM.name}
                  <Tag 
                    icon={getStatusIcon(selectedVM.status)} 
                    color={getStatusColor(selectedVM.status)}
                    style={{ marginLeft: 8 }}
                  >
                    {getTranslatedStatus(selectedVM.status)}
                  </Tag>
                </div>
              }
              className="vm-details-card"
              extra={refreshing && <Spin size="small" />}
            >
              <Tabs 
                activeKey={activeTab} 
                onChange={setActiveTab}
                type="card"
              >
                <TabPane 
                  tab={<span><InfoCircleOutlined />{t('vm_status.overview')}</span>} 
                  key="overview"
                >
                  {renderOverviewTab()}
                </TabPane>
                <TabPane 
                  tab={<span><CodeOutlined />{t('vm_status.injections')}</span>} 
                  key="injections"
                >
                  {renderInjectionsTab()}
                </TabPane>
                <TabPane 
                  tab={<span><EyeOutlined />{t('vm_status.logs')}</span>} 
                  key="logs"
                >
                  {renderLogsTab()}
                </TabPane>
              </Tabs>
            </Card>
          ) : (
            <Alert 
              message={t('vm_status.select_vm')} 
              type="info" 
              showIcon 
            />
          )}
        </div>
      </div>
    </div>
  );
};

export default VmStatusPage; 