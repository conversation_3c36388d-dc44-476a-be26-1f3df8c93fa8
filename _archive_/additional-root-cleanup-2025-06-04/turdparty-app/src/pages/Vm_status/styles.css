.vm-status-container {
  padding: 24px;
  width: 100%;
  transition: opacity 0.3s ease;
  /* Prevent layout shifts by maintaining minimum dimensions */
  min-height: 600px;
  display: flex;
  flex-direction: column;
}

/* Create a content placeholder to maintain layout during loading */
.content-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.7);
  z-index: 5;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.content-placeholder.active {
  opacity: 1;
  pointer-events: auto;
}

/* API disconnection styles */
.api-disconnected {
  position: relative;
}

.api-disconnected::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: repeating-linear-gradient(
    45deg,
    #f5222d,
    #f5222d 10px,
    #ffccc7 10px,
    #ffccc7 20px
  );
  z-index: 10;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.vm-status-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  /* Set a min-height to prevent layout shifting */
  min-height: 40px;
}

.vm-status-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
}

.vm-status-content {
  display: flex;
  gap: 24px;
  flex: 1;
  position: relative;
}

.vm-status-sidebar {
  width: 250px;
  flex-shrink: 0;
  /* Set min-height to prevent collapsing */
  min-height: 400px;
}

.vm-status-details {
  flex-grow: 1;
  min-height: 500px;
  position: relative;
}

/* Add fade transition between refreshes */
.vm-details-card {
  width: 100%;
  transition: opacity 0.3s ease;
  /* Maintain height during refreshes */
  min-height: 500px;
  will-change: opacity;
  transform: translateZ(0);
}

/* Subtle loading state that preserves content */
.refresh-loading .vm-details-card {
  opacity: 0.85;
}

.vm-list-card {
  height: 100%;
  overflow: hidden;
  /* Maintain height during refreshes */
  min-height: 400px;
  position: relative;
}

/* Overlay for list refreshing */
.vm-list-refreshing {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
}

.vm-list-refreshing.active {
  opacity: 1;
}

.vm-list {
  max-height: 500px;
  overflow-y: auto;
  padding-right: 4px;
  /* Maintain position during refreshes */
  position: relative;
  min-height: 100px;
  transition: opacity 0.3s ease;
  will-change: contents;
}

.vm-list-item {
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: all 0.2s ease;
  /* Prevent disappearing during refresh */
  height: 40px;
}

.vm-list-item:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.vm-list-item.selected {
  background-color: rgba(24, 144, 255, 0.1);
  border-left: 3px solid #1890ff;
}

.vm-list-item-name {
  margin-left: 8px;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.vm-status-title {
  display: flex;
  align-items: center;
}

.vm-status-overview,
.vm-status-injections,
.vm-status-logs {
  margin-top: 16px;
  min-height: 200px;
  position: relative;
}

.vm-status-card {
  margin-bottom: 16px;
  transition: all 0.3s ease;
  min-height: 100px;
  overflow: hidden;
  will-change: contents;
}

.resource-usage-container {
  display: flex;
  flex-wrap: wrap;
  gap: 24px;
  margin-top: 16px;
}

.resource-usage-item {
  flex: 1;
  min-width: 200px;
  transition: all 0.3s ease;
  padding: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  /* Maintain height during data changes */
  min-height: 160px;
}

.log-entry {
  margin-bottom: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  font-family: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 13px;
  line-height: 1.4;
}

.log-error {
  background-color: rgba(255, 77, 79, 0.1);
  border-left: 2px solid #ff4d4f;
}

.log-warning {
  background-color: rgba(250, 173, 20, 0.1);
  border-left: 2px solid #faad14;
}

.log-info {
  background-color: rgba(24, 144, 255, 0.1);
  border-left: 2px solid #1890ff;
}

/* Table styles */
.injections-table {
  margin-top: 16px;
}

.injections-table .ant-table-thead > tr > th {
  background-color: #f5f5f5;
  font-weight: 600;
}

.injections-table .ant-table-tbody > tr:hover > td {
  background-color: rgba(24, 144, 255, 0.05);
}

/* Better tab styling */
.vm-details-card .ant-tabs-tab {
  transition: all 0.2s ease;
  padding: 8px 16px;
}

.vm-details-card .ant-tabs-tab-active {
  background-color: rgba(24, 144, 255, 0.1);
}

/* Table minimum height to prevent collapses */
.ant-table-wrapper {
  min-height: 150px;
}

/* Network error alert styling */
.ant-alert-warning.network-error {
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
}

.ant-alert-error.api-error {
  background-color: #fff1f0;
  border: 1px solid #ffa39e;
}

/* Animated loading and refreshing states */
@keyframes fadeInOut {
  0% { opacity: 0.7; }
  50% { opacity: 1; }
  100% { opacity: 0.7; }
}

.refreshing-content {
  animation: fadeInOut 1.5s infinite;
}

/* Better debugging information */
.debug-info {
  margin-top: 20px;
  padding: 12px;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  border-left: 3px solid #1890ff;
}

.debug-info pre {
  margin: 0;
  white-space: pre-wrap;
}

/* Skeleton loader for tables */
.skeleton-loader {
  padding: 16px 0;
}

.skeleton-row {
  height: 20px;
  margin-bottom: 12px;
  background: linear-gradient(90deg, 
    rgba(207, 216, 220, 0.2), 
    rgba(207, 216, 220, 0.4), 
    rgba(207, 216, 220, 0.2));
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 4px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Fix for Ant Design Table reflow issues */
.ant-table-wrapper,
.ant-spin-nested-loading,
.ant-spin-container,
.ant-table,
.ant-table-container,
.ant-table-content {
  will-change: contents;
  transition: none !important;
}

/* Responsive styles */
@media (max-width: 768px) {
  .vm-status-content {
    flex-direction: column;
  }
  
  .vm-status-sidebar {
    width: 100%;
    margin-bottom: 16px;
  }
  
  .resource-usage-item {
    min-width: 100%;
  }
} 