.docs-page-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.docs-page-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
}

.docs-page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.docs-frame-container {
  width: 100%;
  height: calc(100vh - 200px);
  min-height: 600px;
  border-radius: 4px;
  overflow: hidden;
}

.docs-frame {
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 4px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .docs-page-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .docs-page-header > .ant-space:last-child {
    margin-top: 16px;
  }
  
  .docs-frame-container {
    height: calc(100vh - 250px);
  }
} 