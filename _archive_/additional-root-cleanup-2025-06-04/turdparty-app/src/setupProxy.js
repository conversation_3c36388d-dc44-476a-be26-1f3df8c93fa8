const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // Proxy API requests to the backend
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:3050',
      changeOrigin: true,
      secure: false,
      logLevel: 'debug',
      onError: (err, req, res) => {
        console.error('Proxy Error:', err);
        res.status(500).send('Proxy Error');
      },
      onProxyRes: (proxyRes, req, res) => {
        console.log('Proxy Response:', {
          status: proxyRes.statusCode,
          path: req.path,
          method: req.method
        });
      }
    })
  );
}; 