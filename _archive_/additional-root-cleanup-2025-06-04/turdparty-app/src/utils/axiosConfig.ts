import axios from 'axios';
import { API_PREFIX } from './apiConfig';

// Custom error types to improve error handling
export class NetworkError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NetworkError';
  }
}

export class ApiError extends Error {
  status: number;
  data: any;
  
  constructor(message: string, status: number, data: any) {
    super(message);
    this.name = 'ApiError';
    this.status = status;
    this.data = data;
  }
}

export class TimeoutError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'TimeoutError';
  }
}

/**
 * Configure global axios defaults
 */
const configureAxios = () => {
  // Get the API base URL from environment variables
  const baseURL = process.env.REACT_APP_API_URL || '';
  console.log('API Base URL:', baseURL);
  
  // Set base URL if defined
  if (baseURL) {
    axios.defaults.baseURL = baseURL;
  }
  
  // Set default timeout to prevent hanging requests
  axios.defaults.timeout = 15000; // 15 seconds
  
  // Set default headers
  axios.defaults.headers.common['Accept'] = 'application/json';
  
  // Configure to handle redirects automatically
  axios.defaults.maxRedirects = 5;
  
  // Add request interceptor for API versioning
  axios.interceptors.request.use(
    (config) => {
      // If URL doesn't already have API_PREFIX, and starts with /api/
      if (config.url && config.url.startsWith('/api/') && !config.url.startsWith(API_PREFIX)) {
        // Replace /api/ with the correct versioned prefix
        config.url = config.url.replace('/api/', `${API_PREFIX}/`);
        console.debug(`Request URL updated to: ${config.url}`);
      }
      return config;
    },
    (error) => {
      return Promise.reject(error);
    }
  );
  
  // Add response interceptor to handle common errors
  axios.interceptors.response.use(
    (response) => {
      return response;
    },
    (error) => {
      // Handle redirects for API versioning
      if (error.response && error.response.status === 307) {
        const originalRequest = error.config;
        const redirectUrl = error.response.headers.location;
        if (redirectUrl && redirectUrl.includes('/api/v1/')) {
          console.debug(`Following redirect to: ${redirectUrl}`);
          originalRequest.url = redirectUrl;
          return axios(originalRequest);
        }
      }
      
      // Create more detailed error objects
      if (error.code === 'ECONNABORTED') {
        const timeoutError = new TimeoutError('Request timed out. The server is taking too long to respond.');
        return Promise.reject(timeoutError);
      }
      
      if (error.message && (
        error.message.includes('Network Error') || 
        error.message.includes('Failed to fetch') ||
        !error.response
      )) {
        const networkError = new NetworkError(
          'Network error. Please check your internet connection and try again.'
        );
        return Promise.reject(networkError);
      }
      
      if (error.response) {
        // Server responded with an error status code
        let errorMessage = 'The server encountered an error.';
        
        // Handle specific status codes
        switch (error.response.status) {
          case 401:
            errorMessage = 'Authentication required. Please sign in again.';
            break;
          case 403:
            errorMessage = 'You do not have permission to access this resource.';
            break;
          case 404:
            errorMessage = 'The requested resource was not found.';
            break;
          case 500:
            errorMessage = 'The server encountered an internal error.';
            break;
          case 502:
          case 503:
          case 504:
            errorMessage = 'The server is temporarily unavailable. Please try again later.';
            break;
        }
        
        // Use error message from response if available
        if (error.response.data && error.response.data.detail) {
          errorMessage = error.response.data.detail;
        } else if (error.response.data && error.response.data.message) {
          errorMessage = error.response.data.message;
        }
        
        const apiError = new ApiError(
          errorMessage,
          error.response.status,
          error.response.data
        );
        
        return Promise.reject(apiError);
      }
      
      // Log errors in development
      if (process.env.NODE_ENV === 'development') {
        console.error('API Error:', error);
        
        if (error.response) {
          console.error('Status:', error.response.status);
          console.error('Data:', error.response.data);
        } else if (error.request) {
          console.error('Request made but no response received');
        } else {
          console.error('Error setting up request:', error.message);
        }
      }
      
      return Promise.reject(error);
    }
  );
};

export default configureAxios; 