import { logErrorToBackend } from './errorLogger';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import API_ENDPOINTS from './apiConfig';

interface PerformanceMetric {
  type: 'page_load' | 'api_response' | 'component_render';
  name: string;
  duration: number;
  timestamp: string;
  additionalInfo?: Record<string, any>;
}

/**
 * Logs performance metrics to the backend
 * @param metric The performance metric to log
 */
export const logPerformanceMetric = async (metric: PerformanceMetric): Promise<void> => {
  try {
    // Send the metric to the backend
    await axios.post(API_ENDPOINTS.LOGS.PERFORMANCE, metric);
    
    // Also log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.info(`[Performance] ${metric.type} - ${metric.name}: ${metric.duration}ms`, metric);
    }
  } catch (error) {
    // If logging fails, at least log to console
    console.warn('Failed to log performance metric:', error);
    console.info('Original metric:', metric);
  }
};

/**
 * Measures and logs page load time
 * @param pageName The name of the page being loaded
 */
export const measurePageLoad = (pageName: string): void => {
  // Use Performance API to get navigation timing
  if (window.performance) {
    const perfData = window.performance.timing;
    const pageLoadTime = perfData.loadEventEnd - perfData.navigationStart;
    const domLoadTime = perfData.domComplete - perfData.domLoading;
    
    logPerformanceMetric({
      type: 'page_load',
      name: pageName,
      duration: pageLoadTime,
      timestamp: new Date().toISOString(),
      additionalInfo: {
        domLoadTime,
        networkLatency: perfData.responseEnd - perfData.requestStart,
        domContentLoaded: perfData.domContentLoadedEventEnd - perfData.navigationStart,
        redirectTime: perfData.redirectEnd - perfData.redirectStart,
        dnsLookupTime: perfData.domainLookupEnd - perfData.domainLookupStart,
        tcpConnectTime: perfData.connectEnd - perfData.connectStart,
        serverResponseTime: perfData.responseEnd - perfData.requestStart,
        resourcesLoadTime: perfData.loadEventEnd - perfData.domContentLoadedEventEnd
      }
    });
  }
};

/**
 * Creates a higher-order function to measure API response time
 * @param originalFn The original axios request function
 * @param endpoint The API endpoint being called
 */
export const measureApiResponse = <T>(
  originalFn: () => Promise<T>,
  endpoint: string
): () => Promise<T> => {
  return async () => {
    const startTime = performance.now();
    try {
      const result = await originalFn();
      const endTime = performance.now();
      
      logPerformanceMetric({
        type: 'api_response',
        name: endpoint,
        duration: endTime - startTime,
        timestamp: new Date().toISOString()
      });
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      
      logPerformanceMetric({
        type: 'api_response',
        name: endpoint,
        duration: endTime - startTime,
        timestamp: new Date().toISOString(),
        additionalInfo: {
          error: true,
          message: error instanceof Error ? error.message : 'Unknown error'
        }
      });
      
      throw error;
    }
  };
};

/**
 * Sets up performance monitoring for the application
 */
export const setupPerformanceMonitoring = (): void => {
  // Use axios interceptors to measure API response times
  axios.interceptors.request.use((config) => {
    // Add timestamp to the request config
    (config as any)._startTime = performance.now();
    return config;
  });
  
  axios.interceptors.response.use(
    (response) => {
      const endTime = performance.now();
      const startTime = (response.config as any)._startTime || endTime;
      const duration = endTime - startTime;
      const url = response.config.url || 'unknown';
      
      logPerformanceMetric({
        type: 'api_response',
        name: `${response.config.method?.toUpperCase() || 'UNKNOWN'} ${url}`,
        duration,
        timestamp: new Date().toISOString(),
        additionalInfo: {
          status: response.status,
          statusText: response.statusText
        }
      });
      
      return response;
    },
    (error) => {
      const endTime = performance.now();
      const startTime = (error.config as any)?._startTime || endTime;
      const duration = endTime - startTime;
      const url = error.config?.url || 'unknown';
      
      logPerformanceMetric({
        type: 'api_response',
        name: `${error.config?.method?.toUpperCase() || 'UNKNOWN'} ${url}`,
        duration,
        timestamp: new Date().toISOString(),
        additionalInfo: {
          error: true,
          status: error.response?.status,
          statusText: error.response?.statusText,
          message: error.message
        }
      });
      
      return Promise.reject(error);
    }
  );
  
  // Monitor page load performance
  window.addEventListener('load', () => {
    // Use setTimeout to ensure all metrics are available
    setTimeout(() => {
      const path = window.location.pathname;
      const pageName = path === '/' ? 'MainPage' : path.substring(1);
      measurePageLoad(pageName);
    }, 0);
  });
  
  // Monitor resource loading performance
  const observer = new PerformanceObserver((list) => {
    for (const entry of list.getEntries()) {
      // Only log resources that take longer than 500ms to load
      if (entry.duration > 500) {
        const resourceEntry = entry as PerformanceResourceTiming;
        logPerformanceMetric({
          type: 'api_response',
          name: `Resource: ${entry.name}`,
          duration: entry.duration,
          timestamp: new Date().toISOString(),
          additionalInfo: {
            entryType: entry.entryType,
            initiatorType: resourceEntry.initiatorType
          }
        });
      }
    }
  });
  
  observer.observe({ entryTypes: ['resource'] });
};

export default {
  logPerformanceMetric,
  measurePageLoad,
  measureApiResponse,
  setupPerformanceMonitoring
}; 