import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios, { AxiosRequestHeaders } from 'axios';
import API_ENDPOINTS from '../utils/apiConfig';

interface AuthContextType {
  token: string | null;
  user: any | null;
  login: (token: string, user: any) => void;
  logout: () => void;
  isAuthenticated: boolean;
  refreshToken: () => Promise<boolean>;
  authError: string | null;
  isAuthLoading: boolean;
}

// Define a type for the response data
interface TestTokenResponse {
  access_token: string;
  token_type: string;
  [key: string]: any;
}

const AuthContext = createContext<AuthContextType>({
  token: null,
  user: null,
  login: () => {},
  logout: () => {},
  isAuthenticated: false,
  refreshToken: async () => false,
  authError: null,
  isAuthLoading: false,
});

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [token, setToken] = useState<string | null>(localStorage.getItem('authToken'));
  const [user, setUser] = useState<any | null>(
    localStorage.getItem('user') ? JSON.parse(localStorage.getItem('user') || '{}') : null
  );
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [authError, setAuthError] = useState<string | null>(null);
  
  // Debug only: Track request count to detect infinite loops
  const [requestCount, setRequestCount] = useState(0);
  
  // Debug token directly in the console
  useEffect(() => {
    console.log('[Auth Debug] Token state changed:', token ? `${token.substring(0, 15)}...` : null);
    console.log('[Auth Debug] isAuthenticated:', !!token);
  }, [token]);
  
  // Set up axios interceptor for authentication
  useEffect(() => {
    console.log('[Auth Debug] Setting up Axios interceptor');
    
    const requestInterceptor = axios.interceptors.request.use(
      (config) => {
        if (token) {
          // Ensure headers exist and set Authorization
          config.headers = config.headers || {} as AxiosRequestHeaders;
          config.headers['Authorization'] = `Bearer ${token}`;
          
          // Debug: Log requests with token
          console.log(`[Auth Debug] Adding token to request: ${config.url}`);
        } else {
          console.log(`[Auth Debug] No token for request: ${config.url}`);
        }
        
        return config;
      },
      (error) => {
        console.error('[Auth Debug] Request interceptor error:', error);
        return Promise.reject(error);
      }
    );
    
    // Also add response interceptor to catch 401 errors
    const responseInterceptor = axios.interceptors.response.use(
      (response) => response,
      async (error) => {
        if (error.response?.status === 401) {
          console.error('[Auth Debug] 401 Unauthorized response detected');
          setAuthError('Authentication token expired or invalid');
          
          // We don't auto-refresh here to avoid infinite loops
          // The UI will show a refresh button instead
        }
        return Promise.reject(error);
      }
    );
    
    return () => {
      axios.interceptors.request.eject(requestInterceptor);
      axios.interceptors.response.eject(responseInterceptor);
    };
  }, [token]);
  
  // Function to get test token that can be called on demand
  const getTestToken = async (): Promise<boolean> => {
    try {
      setRequestCount(prev => prev + 1);
      
      if (requestCount > 5) {
        console.error('[Auth Debug] Too many token requests, possible infinite loop');
        setAuthError('Too many authentication attempts');
        return false;
      }
      
      console.log('[Auth Debug] Attempting to get test token...');
      
      // Use the direct endpoint with correct HTTP method (POST)
      const response = await axios.post<TestTokenResponse>('http://localhost:3050/api/v1/auth/test-token', {}, {
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('[Auth Debug] Test token response received:', response.status, response.data);
      
      if (response.data.access_token) {
        const testUser = {
          id: '00000000-0000-0000-0000-000000000000',
          username: 'testuser',
          email: '<EMAIL>',
          is_active: true,
          is_superuser: true
        };
        login(response.data.access_token, testUser);
        console.log('[Auth Debug] Successfully obtained test token for authentication');
        setAuthError(null);
        return true;
      }
      
      // If we got here but no token, it's a problem with the response format
      console.error('[Auth Debug] Test token response did not contain access_token:', response.data);
      setAuthError('Invalid response from authentication server');
      return false;
    } catch (error: any) {
      console.error('[Auth Debug] Error getting direct test token:', error.message, error.response?.status);
      
      let errorMessage = 'Failed to obtain authentication token';
      if (error.response?.status) {
        errorMessage += ` (Status: ${error.response.status})`;
      }
      if (error.message) {
        errorMessage += `: ${error.message}`;
      }
      
      setAuthError(errorMessage);
      
      // Don't try fallback if we know the server is unreachable
      if (error.code === 'ECONNREFUSED' || error.message.includes('Network Error')) {
        console.error('[Auth Debug] Server unreachable, not attempting fallback');
        return false;
      }
      
      try {
        // Try a fallback with absolute URL
        console.log('[Auth Debug] Trying fallback test token endpoint...');
        const fallbackResponse = await axios.post<TestTokenResponse>('/api/v1/auth/test-token');
        console.log('[Auth Debug] Fallback test token response:', fallbackResponse.status, fallbackResponse.data);
        
        if (fallbackResponse.data.access_token) {
          login(fallbackResponse.data.access_token, {
            id: '00000000-0000-0000-0000-000000000000',
            username: 'testuser',
            email: '<EMAIL>',
            is_active: true,
            is_superuser: true
          });
          console.log('[Auth Debug] Successfully obtained test token via fallback endpoint');
          setAuthError(null);
          return true;
        }
      } catch (fallbackError: any) {
        console.error('[Auth Debug] Error getting fallback test token:', 
          fallbackError.message, 
          fallbackError.response?.status);
      }
    }
    return false;
  };
  
  // Check token validity function
  const validateToken = async (tokenToCheck: string): Promise<boolean> => {
    try {
      // Use a simple API endpoint to check if the token is valid
      console.log('[Auth Debug] Validating existing token...');
      
      const response = await axios.get('http://localhost:3050/api/v1/health', {
        headers: {
          'Authorization': `Bearer ${tokenToCheck}`
        }
      });
      
      console.log('[Auth Debug] Token validation response:', response.status);
      
      // If we get a 200 OK, the token is valid
      return response.status === 200;
    } catch (error: any) {
      console.error('[Auth Debug] Token validation failed:', 
        error.message, 
        error.response?.status);
      
      // 401 means invalid token
      if (error.response?.status === 401) {
        console.log('[Auth Debug] Token is invalid or expired');
        return false;
      }
      
      // For other errors, assume the token might still be valid
      // to avoid unnecessary token refreshes
      return true;
    }
  };
  
  // Check for test token on initial load if not authenticated
  useEffect(() => {
    const initAuth = async () => {
      setIsLoading(true);
      setAuthError(null);
      
      try {
        console.log('[Auth Debug] Initializing authentication...');
        
        // If we have a token in localStorage, validate it first
        if (token) {
          console.log('[Auth Debug] Found existing token in storage, validating');
          const isValid = await validateToken(token);
          
          if (isValid) {
            console.log('[Auth Debug] Existing token is valid');
            setIsLoading(false);
            return;
          } else {
            console.log('[Auth Debug] Existing token is invalid, removing');
            // Clear invalid token
            localStorage.removeItem('authToken');
            setToken(null);
          }
        }
        
        // If no token or invalid token, get a new one
        console.log('[Auth Debug] No valid token, attempting to get test token');
        await getTestToken();
      } catch (err) {
        console.error('[Auth Debug] Error during auth initialization:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    initAuth();
  }, []);
  
  const login = (newToken: string, newUser: any) => {
    console.log('[Auth Debug] Login called with new token');
    localStorage.setItem('authToken', newToken);
    localStorage.setItem('user', JSON.stringify(newUser));
    setToken(newToken);
    setUser(newUser);
    setAuthError(null);
  };
  
  const logout = () => {
    console.log('[Auth Debug] Logout called');
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    setToken(null);
    setUser(null);
  };

  const refreshToken = async (): Promise<boolean> => {
    console.log('[Auth Debug] Token refresh requested');
    setRequestCount(0); // Reset request count for new refresh cycle
    
    // Clear existing token
    logout();
    
    // Get new token
    const success = await getTestToken();
    
    if (success) {
      console.log('[Auth Debug] Token refresh successful');
    } else {
      console.error('[Auth Debug] Token refresh failed');
    }
    
    return success;
  };
  
  return (
    <AuthContext.Provider
      value={{
        token,
        user,
        login,
        logout,
        isAuthenticated: !!token,
        refreshToken,
        authError,
        isAuthLoading: isLoading,
      }}
    >
      {!isLoading && children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext); 