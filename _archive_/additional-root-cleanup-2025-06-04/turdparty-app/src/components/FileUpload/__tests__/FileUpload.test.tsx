import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { FileUpload } from '../index';
import axios from 'axios';
import { message } from 'antd';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

// Mock antd message
jest.mock('antd', () => ({
  ...jest.requireActual('antd'),
  message: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

describe('FileUpload', () => {
  const mockToken = 'test-token';
  const mockFile = new File(['test'], 'test.txt', { type: 'text/plain' });
  const mockOnUploadSuccess = jest.fn();
  const mockOnUploadError = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders upload area with correct text', () => {
    render(
      <FileUpload
        token={mockToken}
        onUploadSuccess={mockOnUploadSuccess}
        onUploadError={mockOnUploadError}
      />
    );

    expect(screen.getByText(/Click or drag file to this area to upload/i)).toBeInTheDocument();
    expect(screen.getByText(/Support for single file upload/i)).toBeInTheDocument();
  });

  it('shows folder upload text when directory prop is true', () => {
    render(
      <FileUpload
        token={mockToken}
        directory={true}
        multiple={true}
        onUploadSuccess={mockOnUploadSuccess}
        onUploadError={mockOnUploadError}
      />
    );

    expect(screen.getByText(/Click or drag folder to this area to upload/i)).toBeInTheDocument();
    expect(screen.getByText(/Support for multiple file uploads/i)).toBeInTheDocument();
  });

  it('validates file size', async () => {
    const largeFile = new File(['x'.repeat(101 * 1024 * 1024)], 'large.txt', { type: 'text/plain' });
    
    render(
      <FileUpload
        token={mockToken}
        maxFileSize={100 * 1024 * 1024} // 100MB
        onUploadSuccess={mockOnUploadSuccess}
        onUploadError={mockOnUploadError}
      />
    );

    const uploadInput = screen.getByRole('button');
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(largeFile);

    fireEvent.drop(uploadInput, {
      dataTransfer,
    });

    expect(message.error).toHaveBeenCalledWith(expect.stringContaining('too large'));
  });

  it('validates file type', async () => {
    const imageFile = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
    
    render(
      <FileUpload
        token={mockToken}
        acceptedFileTypes={['text/plain']}
        onUploadSuccess={mockOnUploadSuccess}
        onUploadError={mockOnUploadError}
      />
    );

    const uploadInput = screen.getByRole('button');
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(imageFile);

    fireEvent.drop(uploadInput, {
      dataTransfer,
    });

    expect(message.error).toHaveBeenCalledWith(expect.stringContaining('not an accepted file type'));
  });

  it('handles successful file upload', async () => {
    const mockResponse = {
      data: {
        id: '123',
        filename: 'test.txt',
      },
    };
    mockedAxios.post.mockResolvedValueOnce(mockResponse);

    render(
      <FileUpload
        token={mockToken}
        onUploadSuccess={mockOnUploadSuccess}
        onUploadError={mockOnUploadError}
      />
    );

    const uploadInput = screen.getByRole('button');
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(mockFile);

    fireEvent.drop(uploadInput, {
      dataTransfer,
    });

    const uploadButton = screen.getByText('Upload');
    fireEvent.click(uploadButton);

    await waitFor(() => {
      expect(mockedAxios.post).toHaveBeenCalled();
      expect(mockOnUploadSuccess).toHaveBeenCalledWith(mockResponse.data);
      expect(message.success).toHaveBeenCalledWith('File uploaded successfully');
    });
  });

  it('handles upload error', async () => {
    const mockError = new Error('Upload failed');
    mockedAxios.post.mockRejectedValueOnce(mockError);

    render(
      <FileUpload
        token={mockToken}
        onUploadSuccess={mockOnUploadSuccess}
        onUploadError={mockOnUploadError}
      />
    );

    const uploadInput = screen.getByRole('button');
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(mockFile);

    fireEvent.drop(uploadInput, {
      dataTransfer,
    });

    const uploadButton = screen.getByText('Upload');
    fireEvent.click(uploadButton);

    await waitFor(() => {
      expect(mockedAxios.post).toHaveBeenCalled();
      expect(mockOnUploadError).toHaveBeenCalledWith(mockError);
      expect(message.error).toHaveBeenCalledWith('Failed to upload file(s)');
    });
  });

  it('shows progress during upload', async () => {
    const mockResponse = {
      data: {
        id: '123',
        filename: 'test.txt',
      },
    };
    mockedAxios.post.mockImplementationOnce(() => {
      return new Promise((resolve) => {
        setTimeout(() => resolve(mockResponse), 100);
      });
    });

    render(
      <FileUpload
        token={mockToken}
        onUploadSuccess={mockOnUploadSuccess}
        onUploadError={mockOnUploadError}
      />
    );

    const uploadInput = screen.getByRole('button');
    const dataTransfer = new DataTransfer();
    dataTransfer.items.add(mockFile);

    fireEvent.drop(uploadInput, {
      dataTransfer,
    });

    const uploadButton = screen.getByText('Upload');
    fireEvent.click(uploadButton);

    await waitFor(() => {
      expect(screen.getByText('Uploading...')).toBeInTheDocument();
    });

    await waitFor(() => {
      expect(mockOnUploadSuccess).toHaveBeenCalledWith(mockResponse.data);
    });
  });
}); 