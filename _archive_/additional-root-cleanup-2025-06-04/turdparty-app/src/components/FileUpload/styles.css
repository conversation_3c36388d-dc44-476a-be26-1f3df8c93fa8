.file-upload-component {
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
}

.file-upload-component .ant-upload-drag {
  border: 2px dashed #d9d9d9;
  border-radius: 8px;
  background: #fafafa;
  transition: all 0.3s ease;
}

/* Add dark mode styles for upload area */
:root[theme-mode="dark"] .file-upload-component .ant-upload-drag,
.ant-app[class*="-dark"] .file-upload-component .ant-upload-drag,
.ant-app-dark .file-upload-component .ant-upload-drag {
  border-color: #434343;
  background: #1f1f1f;
}

.file-upload-component .ant-upload-drag:hover {
  border-color: #1890ff;
  background: #f0f7ff;
}

/* Add dark mode hover state */
:root[theme-mode="dark"] .file-upload-component .ant-upload-drag:hover,
.ant-app[class*="-dark"] .file-upload-component .ant-upload-drag:hover,
.ant-app-dark .file-upload-component .ant-upload-drag:hover {
  border-color: #177ddc;
  background: #111a2c;
}

.file-upload-component .ant-upload-drag-icon {
  margin-bottom: 16px;
  color: #1890ff;
}

.file-upload-component .ant-upload-drag-icon .anticon {
  font-size: 48px;
}

.file-upload-component .ant-upload-text {
  margin: 0 0 8px;
  color: rgba(0, 0, 0, 0.85);
  font-size: 16px;
  font-weight: 500;
}

/* Add dark mode text color */
:root[theme-mode="dark"] .file-upload-component .ant-upload-text,
.ant-app[class*="-dark"] .file-upload-component .ant-upload-text,
.ant-app-dark .file-upload-component .ant-upload-text {
  color: rgba(255, 255, 255, 0.85);
}

.file-upload-component .ant-upload-hint {
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  line-height: 1.5;
}

/* Add dark mode hint color */
:root[theme-mode="dark"] .file-upload-component .ant-upload-hint,
.ant-app[class*="-dark"] .file-upload-component .ant-upload-hint,
.ant-app-dark .file-upload-component .ant-upload-hint {
  color: rgba(255, 255, 255, 0.45);
}

.file-upload-component .upload-error-alert {
  margin-bottom: 16px;
}

.file-upload-component .ant-progress {
  margin: 16px 0;
}

/* Upload progress card styles */
.file-upload-component .upload-progress-card {
  margin: 16px 0 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.file-upload-component.dark-mode .upload-progress-card {
  background-color: #1f1f1f;
  border: 1px solid #303030;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.file-upload-component .upload-progress-card .ant-card-body {
  padding: 16px 20px;
}

.file-upload-component .upload-stats {
  margin-top: 16px;
  display: flex;
  justify-content: center;
  padding: 8px 0;
}

.file-upload-component .upload-stats .ant-statistic {
  text-align: center;
  min-width: 100px;
}

.file-upload-component .upload-stats .ant-statistic-title {
  font-size: 12px;
  margin-bottom: 4px;
}

.file-upload-component .upload-stats .ant-statistic-content {
  font-size: 16px;
  line-height: 1.4;
}

.file-upload-component.dark-mode .upload-stats .ant-statistic-title {
  color: rgba(255, 255, 255, 0.65);
}

.file-upload-component.dark-mode .upload-stats .ant-statistic-content {
  color: rgba(255, 255, 255, 0.85);
}

/* Override ant-progress colors for dark mode */
.file-upload-component.dark-mode .ant-progress-text {
  color: rgba(255, 255, 255, 0.85);
}

.file-upload-component.dark-mode .ant-progress-bg {
  background-image: linear-gradient(to right, #108ee9, #87d068);
}

.file-upload-component .upload-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 120px;
  height: 40px;
  padding: 0 16px;
  background-color: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
}

.file-upload-component .upload-button:hover:not(:disabled) {
  background-color: #40a9ff;
}

.file-upload-component .upload-button:active:not(:disabled) {
  background-color: #096dd9;
}

.file-upload-component .upload-button:disabled {
  background-color: #d9d9d9;
  cursor: not-allowed;
}

.file-upload-component .upload-button.uploading {
  background-color: #1890ff;
  opacity: 0.8;
  cursor: wait;
}

/* More comprehensive dark mode styles */
body.dark-mode .file-upload-component .ant-upload-drag,
[data-theme="dark"] .file-upload-component .ant-upload-drag {
  border-color: #434343;
  background: #1f1f1f;
}

body.dark-mode .file-upload-component .ant-upload-drag:hover,
[data-theme="dark"] .file-upload-component .ant-upload-drag:hover {
  border-color: #177ddc;
  background: #111a2c;
}

body.dark-mode .file-upload-component .ant-upload-text,
[data-theme="dark"] .file-upload-component .ant-upload-text {
  color: rgba(255, 255, 255, 0.85);
}

body.dark-mode .file-upload-component .ant-upload-hint,
[data-theme="dark"] .file-upload-component .ant-upload-hint {
  color: rgba(255, 255, 255, 0.45);
}

/* Target the component's own dark mode class */
.file-upload-component.dark-mode .ant-upload-drag {
  border-color: #434343;
  background: #1f1f1f;
}

.file-upload-component.dark-mode .ant-upload-drag:hover {
  border-color: #177ddc;
  background: #111a2c;
}

.file-upload-component.dark-mode .ant-upload-text {
  color: rgba(255, 255, 255, 0.85);
}

.file-upload-component.dark-mode .ant-upload-hint {
  color: rgba(255, 255, 255, 0.45);
}

.file-upload-component.dark-mode .upload-button {
  background-color: #177ddc;
}

.file-upload-component.dark-mode .upload-button:hover:not(:disabled) {
  background-color: #2b8dec;
}

.file-upload-component.dark-mode .upload-button:active:not(:disabled) {
  background-color: #1068bf;
}

.file-upload-component.dark-mode .upload-button:disabled {
  background-color: #303030;
}

.file-upload-component.dark-mode .ant-upload-drag-icon {
  color: #177ddc;  /* Darker blue for dark mode */
}

.file-upload-component.dark-mode .ant-input,
.file-upload-component.dark-mode .ant-input-textarea {
  background-color: #141414;
  border-color: #434343;
  color: rgba(255, 255, 255, 0.85);
}

.file-upload-component.dark-mode .ant-input:hover,
.file-upload-component.dark-mode .ant-input-textarea:hover {
  border-color: #165996;
}

.file-upload-component.dark-mode .ant-input:focus,
.file-upload-component.dark-mode .ant-input-textarea:focus {
  border-color: #177ddc;
  box-shadow: 0 0 0 2px rgba(23, 125, 220, 0.2);
}

.file-upload-component.dark-mode .ant-form-item-label > label {
  color: rgba(255, 255, 255, 0.85);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .file-upload-component {
    max-width: 100%;
    padding: 0 16px;
  }
  
  .file-upload-component .upload-stats {
    flex-wrap: wrap;
  }
  
  .file-upload-component .upload-stats .ant-statistic {
    min-width: 80px;
    margin: 4px 8px;
  }
  
  .file-upload-component .upload-stats .ant-statistic-title {
    font-size: 11px;
  }
  
  .file-upload-component .upload-stats .ant-statistic-content {
    font-size: 14px;
  }
  
  .file-upload-component .ant-upload-drag-icon .anticon {
    font-size: 36px;
  }
  
  .file-upload-component .ant-upload-text {
    font-size: 14px;
  }
  
  .file-upload-component .ant-upload-hint {
    font-size: 12px;
  }
} 