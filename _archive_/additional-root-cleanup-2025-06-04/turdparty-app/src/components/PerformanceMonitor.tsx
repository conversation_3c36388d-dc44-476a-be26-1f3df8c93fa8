import React, { useState, useEffect } from 'react';
import { Card, Table, Tabs, Spin, Typography, Alert, Button, Space, Select, InputNumber } from 'antd';
import axios from 'axios';
import API_ENDPOINTS from '../utils/apiConfig';
// Note: You'll need to install recharts package with:
// npm install --save recharts @types/recharts
// import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, ResponsiveContainer } from 'recharts';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;

interface PerformanceMetric {
  timestamp: string;
  type: string;
  name: string;
  duration: number;
  additionalInfo?: Record<string, any>;
  user?: {
    user_id: string;
    username: string;
  };
}

interface PerformanceSummary {
  metrics: PerformanceMetric[];
  count: number;
  average_duration: number;
}

interface ChartDataItem {
  name: string;
  avgDuration: number;
  count: number;
}

const PerformanceMonitor: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [summary, setSummary] = useState<PerformanceSummary | null>(null);
  const [metricType, setMetricType] = useState<string | null>(null);
  const [page, setPage] = useState<string | null>(null);
  const [limit, setLimit] = useState<number>(100);
  
  const fetchPerformanceData = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Build query parameters
      const params = new URLSearchParams();
      if (metricType) params.append('metric_type', metricType);
      if (page) params.append('page', page);
      params.append('limit', limit.toString());
      
      const response = await axios.get<PerformanceSummary>(`${API_ENDPOINTS.LOGS.PERFORMANCE_SUMMARY}?${params.toString()}`);
      setSummary(response.data);
    } catch (err) {
      setError('Failed to load performance data');
      console.error('Error fetching performance data:', err);
    } finally {
      setLoading(false);
    }
  };
  
  useEffect(() => {
    fetchPerformanceData();
  }, [metricType, page, limit]);
  
  // Prepare data for charts
  const prepareChartData = (): ChartDataItem[] => {
    if (!summary?.metrics) return [];
    
    // Group by name and calculate average duration
    const groupedData: Record<string, { name: string, count: number, totalDuration: number }> = {};
    
    summary.metrics.forEach(metric => {
      if (!groupedData[metric.name]) {
        groupedData[metric.name] = {
          name: metric.name,
          count: 0,
          totalDuration: 0
        };
      }
      
      groupedData[metric.name].count += 1;
      groupedData[metric.name].totalDuration += metric.duration;
    });
    
    return Object.values(groupedData).map(item => ({
      name: item.name,
      avgDuration: item.totalDuration / item.count,
      count: item.count
    }));
  };
  
  const chartData = prepareChartData();
  
  // Extract unique metric types and page names for filters
  const metricTypes = summary?.metrics 
    ? Array.from(new Set(summary.metrics.map(m => m.type)))
    : [];
    
  const pageNames = summary?.metrics 
    ? Array.from(new Set(summary.metrics.map(m => m.name)))
    : [];
  
  const columns = [
    {
      title: 'Timestamp',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (text: string) => new Date(text).toLocaleString()
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
    },
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
    },
    {
      title: 'Duration (ms)',
      dataIndex: 'duration',
      key: 'duration',
      sorter: (a: PerformanceMetric, b: PerformanceMetric) => a.duration - b.duration,
      render: (duration: number) => duration.toFixed(2)
    },
    {
      title: 'User',
      dataIndex: 'user',
      key: 'user',
      render: (user?: { username: string }) => user?.username || 'Anonymous'
    }
  ];
  
  return (
    <Card title="Performance Monitoring" style={{ margin: '20px' }}>
      {error && (
        <Alert
          message="Error"
          description={error}
          type="error"
          showIcon
          style={{ marginBottom: '20px' }}
        />
      )}
      
      <Space direction="vertical" style={{ width: '100%', marginBottom: '20px' }}>
        <Title level={4}>Filters</Title>
        <Space>
          <Select
            placeholder="Filter by metric type"
            style={{ width: 200 }}
            allowClear
            onChange={(value: string | null) => setMetricType(value)}
            value={metricType}
          >
            {metricTypes.map(type => (
              <Option key={type} value={type}>{type}</Option>
            ))}
          </Select>
          
          <Select
            placeholder="Filter by page/endpoint"
            style={{ width: 300 }}
            allowClear
            onChange={(value: string | null) => setPage(value)}
            value={page}
          >
            {pageNames.map(name => (
              <Option key={name} value={name}>{name}</Option>
            ))}
          </Select>
          
          <Text>Limit:</Text>
          <InputNumber
            min={10}
            max={1000}
            value={limit}
            onChange={(value: number | null) => setLimit(value || 100)}
          />
          
          <Button type="primary" onClick={fetchPerformanceData}>
            Refresh
          </Button>
        </Space>
      </Space>
      
      <Tabs defaultActiveKey="summary">
        <TabPane tab="Summary" key="summary">
          {loading ? (
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <Spin size="large" />
            </div>
          ) : (
            <Space direction="vertical" style={{ width: '100%' }}>
              <Card>
                <Title level={4}>Performance Overview</Title>
                <Space direction="horizontal">
                  <Card size="small" title="Total Metrics">
                    <Title level={2}>{summary?.count || 0}</Title>
                  </Card>
                  <Card size="small" title="Average Duration (ms)">
                    <Title level={2}>{summary?.average_duration.toFixed(2) || 0}</Title>
                  </Card>
                </Space>
              </Card>
              
              <Card title="Average Duration by Endpoint/Page">
                <div style={{ height: 300, width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
                  <Text>Chart will be available after installing recharts package</Text>
                  {/* 
                  <ResponsiveContainer width="100%" height={300}>
                    <BarChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip formatter={(value) => `${Number(value).toFixed(2)} ms`} />
                      <Legend />
                      <Bar dataKey="avgDuration" name="Avg Duration (ms)" fill="#8884d8" />
                    </BarChart>
                  </ResponsiveContainer>
                  */}
                </div>
              </Card>
            </Space>
          )}
        </TabPane>
        
        <TabPane tab="Raw Data" key="raw">
          {loading ? (
            <div style={{ textAlign: 'center', padding: '50px' }}>
              <Spin size="large" />
            </div>
          ) : (
            <Table
              dataSource={summary?.metrics || []}
              columns={columns}
              rowKey={(record) => `${record.timestamp}-${record.name}`}
              pagination={{ pageSize: 10 }}
            />
          )}
        </TabPane>
      </Tabs>
    </Card>
  );
};

export default PerformanceMonitor; 