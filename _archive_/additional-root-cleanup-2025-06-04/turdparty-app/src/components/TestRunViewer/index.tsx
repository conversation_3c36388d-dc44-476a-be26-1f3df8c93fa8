import React, { useState, useEffect } from 'react';
import { Card, Table, Typography, Progress, Tabs, Space, Tag, Spin, Alert, Button } from 'antd';
import { ReloadOutlined, CheckCircleOutlined, CloseCircleOutlined, WarningOutlined } from '@ant-design/icons';
import axios from 'axios';
import API_ENDPOINTS from '../../utils/apiConfig';
import { useAuth } from '../../hooks/useAuth';
import './styles.css';

const { Title, Text, Paragraph } = Typography;

interface TestModule {
  file: string;
  missing_lines: number;
  percentage: number;
  statements: number;
}

interface TestResult {
  status: string;
  timestamp: string;
}

interface TestResultSummary {
  [key: string]: TestResult;
}

interface CoverageData {
  covered_lines: number;
  missing_lines: number;
  modules: TestModule[];
  needs_improvement: TestModule[];
  num_statements: number;
  source: string;
  timestamp: string;
  total_percentage: number;
}

interface TestRunResponse {
  latest_coverage: CoverageData;
  status: string;
  test_results: {
    available: boolean;
    files: string[];
    summary: TestResultSummary;
  };
  timestamp: string;
}

export interface TestRunViewerProps {
  onRefresh?: () => void;
}

export const TestRunViewer: React.FC<TestRunViewerProps> = ({ onRefresh }) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [testRunData, setTestRunData] = useState<TestRunResponse | null>(null);
  const { token } = useAuth();

  useEffect(() => {
    fetchTestRunData();
  }, [token]);

  const fetchTestRunData = async () => {
    if (!token) {
      setError('Authentication token is missing');
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await axios.get(API_ENDPOINTS.HEALTH.TEST_RUNS, {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      setTestRunData(response.data);
    } catch (error: any) {
      console.error('Error fetching test run data:', error);
      setError(
        error.response?.data?.detail || 
        error.message || 
        'Failed to fetch test run data'
      );
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    fetchTestRunData();
    if (onRefresh) {
      onRefresh();
    }
  };

  const getStatusTag = (status: string) => {
    switch (status.toLowerCase()) {
      case 'passed':
        return <Tag color="success" icon={<CheckCircleOutlined />}>Passed</Tag>;
      case 'failed':
        return <Tag color="error" icon={<CloseCircleOutlined />}>Failed</Tag>;
      case 'skipped':
        return <Tag color="warning" icon={<WarningOutlined />}>Skipped</Tag>;
      default:
        return <Tag color="default">{status}</Tag>;
    }
  };

  const coverageColumns = [
    {
      title: 'Module',
      dataIndex: 'file',
      key: 'file',
      sorter: (a: TestModule, b: TestModule) => a.file.localeCompare(b.file),
    },
    {
      title: 'Coverage',
      dataIndex: 'percentage',
      key: 'percentage',
      sorter: (a: TestModule, b: TestModule) => a.percentage - b.percentage,
      render: (percentage: number) => (
        <Space direction="vertical" style={{ width: '100%' }}>
          <Progress 
            percent={percentage} 
            size="small" 
            status={percentage < 50 ? 'exception' : percentage < 80 ? 'normal' : 'success'}
          />
          <Text>{percentage.toFixed(1)}%</Text>
        </Space>
      ),
    },
    {
      title: 'Statements',
      dataIndex: 'statements',
      key: 'statements',
      sorter: (a: TestModule, b: TestModule) => a.statements - b.statements,
    },
    {
      title: 'Missing Lines',
      dataIndex: 'missing_lines',
      key: 'missing_lines',
      sorter: (a: TestModule, b: TestModule) => a.missing_lines - b.missing_lines,
    },
  ];

  const formatTimestamp = (timestamp: string) => {
    try {
      const date = new Date(timestamp);
      return date.toLocaleString();
    } catch (e) {
      return timestamp;
    }
  };

  if (loading) {
    return (
      <div className="test-run-viewer">
        <Card>
          <Spin tip="Loading test run data...">
            <div style={{ height: 300 }} />
          </Spin>
        </Card>
      </div>
    );
  }

  if (error) {
    return (
      <div className="test-run-viewer">
        <Card>
          <Alert
            message="Error Loading Test Data"
            description={error}
            type="error"
            showIcon
            action={
              <Button 
                icon={<ReloadOutlined />} 
                onClick={handleRefresh}
              >
                Retry
              </Button>
            }
          />
        </Card>
      </div>
    );
  }

  if (!testRunData) {
    return (
      <div className="test-run-viewer">
        <Card>
          <Alert
            message="No Test Data Available"
            description="No test run data is currently available."
            type="info"
            showIcon
            action={
              <Button 
                icon={<ReloadOutlined />} 
                onClick={handleRefresh}
              >
                Refresh
              </Button>
            }
          />
        </Card>
      </div>
    );
  }

  const { latest_coverage, test_results, timestamp } = testRunData;

  return (
    <div className="test-run-viewer">
      <Card
        title={
          <Space>
            <Title level={4} style={{ margin: 0 }}>Test Run Summary</Title>
            <Text type="secondary">Last Updated: {formatTimestamp(timestamp)}</Text>
          </Space>
        }
        extra={
          <Button 
            icon={<ReloadOutlined />} 
            onClick={handleRefresh}
          >
            Refresh
          </Button>
        }
      >
        <Tabs 
          defaultActiveKey="coverage"
          items={[
            {
              key: 'coverage',
              label: 'Coverage Summary',
              children: (
                <>
                  <Card className="summary-card">
                    <Space direction="vertical" style={{ width: '100%' }}>
                      <div className="coverage-summary">
                        <Title level={4}>Overall Coverage: {latest_coverage.total_percentage.toFixed(1)}%</Title>
                        <Progress 
                          percent={latest_coverage.total_percentage} 
                          status={
                            latest_coverage.total_percentage < 50 ? 'exception' : 
                            latest_coverage.total_percentage < 80 ? 'normal' : 'success'
                          }
                          strokeWidth={15}
                        />
                      </div>
                      <Paragraph>
                        <Text strong>{latest_coverage.covered_lines}</Text> of <Text strong>{latest_coverage.num_statements}</Text> lines covered
                        (<Text strong>{latest_coverage.missing_lines}</Text> lines missing)
                      </Paragraph>
                      <Paragraph>Coverage data from: <Text code>{latest_coverage.source}</Text></Paragraph>
                    </Space>
                  </Card>

                  <Title level={5} style={{ marginTop: 16 }}>Module Coverage Details</Title>
                  <Table 
                    dataSource={latest_coverage.modules.map(module => ({ ...module, key: module.file }))}
                    columns={coverageColumns}
                    pagination={false}
                    size="small"
                    scroll={{ y: 300 }}
                  />
                </>
              )
            },
            {
              key: 'improvement',
              label: 'Needs Improvement',
              children: (
                <>
                  <Alert
                    message="Modules Needing Improvement"
                    description="These modules have less than 50% code coverage and should be prioritized for additional tests."
                    type="warning"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />
                  <Table 
                    dataSource={latest_coverage.needs_improvement.map(module => ({ ...module, key: module.file }))}
                    columns={coverageColumns}
                    pagination={false}
                    size="small"
                  />
                </>
              )
            },
            {
              key: 'test-results',
              label: 'Test Results',
              children: (
                <Card className="summary-card">
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <Title level={5}>Available Test Files:</Title>
                    <ul>
                      {test_results.files.map(file => (
                        <li key={file}>{file}</li>
                      ))}
                    </ul>
                    
                    <Title level={5}>Test Results Summary:</Title>
                    <Table 
                      dataSource={Object.entries(test_results.summary).map(([module, result]) => ({
                        key: module,
                        module,
                        status: result.status,
                        timestamp: result.timestamp,
                      }))}
                      columns={[
                        {
                          title: 'Module',
                          dataIndex: 'module',
                          key: 'module',
                        },
                        {
                          title: 'Status',
                          dataIndex: 'status',
                          key: 'status',
                          render: (status: string) => getStatusTag(status),
                        },
                        {
                          title: 'Timestamp',
                          dataIndex: 'timestamp',
                          key: 'timestamp',
                          render: (timestamp: string) => formatTimestamp(timestamp),
                        },
                      ]}
                      pagination={false}
                      size="small"
                    />
                  </Space>
                </Card>
              )
            }
          ]}
        />
      </Card>
    </div>
  );
};

export default TestRunViewer; 