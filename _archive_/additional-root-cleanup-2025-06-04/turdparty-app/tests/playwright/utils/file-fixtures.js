// @ts-check
const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

// Base directory for test files
const FIXTURES_DIR = path.join(__dirname, '../fixtures/test-files');

/**
 * Ensure the fixtures directory exists
 */
function ensureFixturesDir() {
  if (!fs.existsSync(FIXTURES_DIR)) {
    fs.mkdirSync(FIXTURES_DIR, { recursive: true });
  }
}

/**
 * Create a test file with random data
 * @param {string} filename - Name of the file to create
 * @param {number} sizeInKB - Size of the file in KB
 * @returns {string} Full path to the created file
 */
function createRandomFile(filename, sizeInKB = 100) {
  ensureFixturesDir();
  
  const filePath = path.join(FIXTURES_DIR, filename);
  
  // Create random content
  const buffer = Buffer.alloc(sizeInKB * 1024);
  for (let i = 0; i < buffer.length; i++) {
    buffer[i] = Math.floor(Math.random() * 256);
  }
  
  fs.writeFileSync(filePath, buffer);
  console.log(`Created random file: ${filePath} (${sizeInKB} KB)`);
  
  return filePath;
}

/**
 * Create a simple ISO file (simulated)
 * @param {string} filename - Name of the ISO file
 * @param {number} sizeInKB - Size in KB
 * @returns {string} Full path to the created file
 */
function createISOFile(filename, sizeInKB = 1024) {
  ensureFixturesDir();
  
  if (!filename.endsWith('.iso')) {
    filename = `${filename}.iso`;
  }
  
  const filePath = path.join(FIXTURES_DIR, filename);
  
  // Create a buffer with ISO-like header
  const buffer = Buffer.alloc(sizeInKB * 1024);
  
  // Add "CD001" at offset 32769 to simulate ISO9660 identifier
  const isoMarker = Buffer.from('CD001');
  isoMarker.copy(buffer, 32769);
  
  // Fill rest with random data
  crypto.randomFillSync(buffer, 32769 + isoMarker.length);
  
  fs.writeFileSync(filePath, buffer);
  console.log(`Created ISO file: ${filePath} (${sizeInKB} KB)`);
  
  return filePath;
}

/**
 * Create a text file with specified content
 * @param {string} filename - Name of the text file
 * @param {string} content - Text content
 * @returns {string} Full path to the created file
 */
function createTextFile(filename, content) {
  ensureFixturesDir();
  
  if (!path.extname(filename)) {
    filename = `${filename}.txt`;
  }
  
  const filePath = path.join(FIXTURES_DIR, filename);
  fs.writeFileSync(filePath, content, 'utf8');
  
  console.log(`Created text file: ${filePath} (${content.length} bytes)`);
  return filePath;
}

/**
 * Clean up all test files or specific files
 * @param {string[]} [filenames] - Optional specific files to clean up
 */
function cleanupTestFiles(filenames = []) {
  if (!fs.existsSync(FIXTURES_DIR)) {
    return;
  }
  
  if (filenames.length === 0) {
    // Clean up all files
    const files = fs.readdirSync(FIXTURES_DIR);
    files.forEach(file => {
      const filePath = path.join(FIXTURES_DIR, file);
      fs.unlinkSync(filePath);
      console.log(`Deleted test file: ${filePath}`);
    });
  } else {
    // Clean up specific files
    filenames.forEach(filename => {
      const filePath = path.join(FIXTURES_DIR, filename);
      if (fs.existsSync(filePath)) {
        fs.unlinkSync(filePath);
        console.log(`Deleted test file: ${filePath}`);
      }
    });
  }
}

/**
 * Generate a unique filename with timestamp
 * @param {string} prefix - Prefix for the filename
 * @param {string} extension - File extension (without dot)
 * @returns {string} Unique filename with timestamp
 */
function generateUniqueFilename(prefix = 'test', extension = 'iso') {
  const timestamp = Date.now();
  return `${prefix}-${timestamp}.${extension}`;
}

module.exports = {
  createRandomFile,
  createISOFile,
  createTextFile,
  cleanupTestFiles,
  generateUniqueFilename,
  FIXTURES_DIR,
}; 